<?php
/**
 * SSL Status Checker
 * Shows all available certificates and domain configurations
 */

echo "=== SSL Status Checker ===\n\n";

// Check available certificates
echo "=== Available SSL Certificates ===\n";
exec("sudo find /etc/letsencrypt/live -maxdepth 1 -type d 2>/dev/null | grep -v '^/etc/letsencrypt/live$'", $certDirs);

foreach ($certDirs as $certDir) {
    $certName = basename($certDir);
    exec("sudo test -f '{$certDir}/fullchain.pem' && sudo test -f '{$certDir}/privkey.pem'", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✅ {$certName}\n";
        
        // Get certificate expiry
        exec("sudo openssl x509 -in '{$certDir}/fullchain.pem' -noout -enddate 2>/dev/null", $expiryOutput);
        if (!empty($expiryOutput)) {
            $expiry = str_replace('notAfter=', '', $expiryOutput[0]);
            echo "   Expires: {$expiry}\n";
        }
        
        // Get certificate domains
        exec("sudo openssl x509 -in '{$certDir}/fullchain.pem' -noout -text 2>/dev/null | grep -A 1 'Subject Alternative Name'", $sanOutput);
        if (!empty($sanOutput)) {
            echo "   Domains: " . trim(str_replace(['DNS:', 'Subject Alternative Name:'], '', implode(' ', $sanOutput))) . "\n";
        }
        echo "\n";
    } else {
        echo "❌ {$certName} (missing files)\n\n";
    }
}

// Check configured domains
echo "=== Configured Custom Domains ===\n";
exec("sudo ls /etc/apache2/sites-available/*.conf 2>/dev/null | grep -E '\\.(com|top|net|org)\\.conf$'", $domainConfigs);

foreach ($domainConfigs as $configFile) {
    $domain = basename($configFile, '.conf');
    
    // Check if site is enabled
    $enabledFile = "/etc/apache2/sites-enabled/" . basename($configFile);
    $isEnabled = file_exists($enabledFile);
    
    echo ($isEnabled ? "✅" : "❌") . " {$domain} " . ($isEnabled ? "(enabled)" : "(disabled)") . "\n";
    
    if ($isEnabled) {
        // Check SSL configuration
        exec("sudo grep 'SSLCertificateFile' '{$configFile}' 2>/dev/null", $sslConfig);
        if (!empty($sslConfig)) {
            $certPath = trim(str_replace('SSLCertificateFile', '', $sslConfig[0]));
            echo "   SSL: {$certPath}\n";
            
            // Test if certificate file exists
            exec("sudo test -f '{$certPath}'", $output, $returnCode);
            echo "   Certificate: " . ($returnCode === 0 ? "✅ Found" : "❌ Missing") . "\n";
        } else {
            echo "   SSL: ❌ Not configured\n";
        }
        
        // Test HTTP/HTTPS access
        exec("curl -I http://{$domain} --max-time 5 2>/dev/null | head -1", $httpTest);
        exec("curl -I https://{$domain} --insecure --max-time 5 2>/dev/null | head -1", $httpsTest);
        
        echo "   HTTP: " . (!empty($httpTest) ? "✅ " . trim($httpTest[0]) : "❌ Not accessible") . "\n";
        echo "   HTTPS: " . (!empty($httpsTest) ? "✅ " . trim($httpsTest[0]) : "❌ Not accessible") . "\n";
    }
    echo "\n";
}

// Show recommendations
echo "=== Recommendations ===\n";

$workingCerts = [];
foreach ($certDirs as $certDir) {
    exec("sudo test -f '{$certDir}/fullchain.pem' && sudo test -f '{$certDir}/privkey.pem'", $output, $returnCode);
    if ($returnCode === 0) {
        $workingCerts[] = $certDir;
    }
}

if (count($workingCerts) > 0) {
    echo "✅ You have " . count($workingCerts) . " working SSL certificates\n";
    echo "💡 For new domains, you can use: php fix_domain_ssl.php domain.name\n";
    echo "💡 Best certificate for loanratelk.top domains: " . $workingCerts[0] . "\n";
} else {
    echo "❌ No working SSL certificates found\n";
    echo "💡 You need to create SSL certificates first\n";
}

echo "\n=== Status Check Complete ===\n";
