<?php
/**
 * Advanced SSL Domain Manager for SaaS Custom Domains
 * Handles automatic SSL certificate management
 */

class SSLDomainManager 
{
    private $certPath = '/etc/letsencrypt/live/interrandadmin.com';
    private $apachePath = '/etc/apache2/sites-available/';
    
    public function __construct() {
        echo "=== SaaS SSL Domain Manager ===\n\n";
    }
    
    /**
     * Fix SSL for a specific domain
     */
    public function fixDomainSSL($domain) {
        echo "🔧 Fixing SSL for: {$domain}\n";
        
        $configFile = $this->apachePath . $domain . '.conf';
        
        if (!file_exists($configFile)) {
            echo "❌ Apache config not found: {$configFile}\n";
            return false;
        }
        
        // Update SSL certificate paths
        $commands = [
            "sudo sed -i 's|SSLCertificateFile /etc/ssl/certs/ssl-cert-snakeoil.pem|SSLCertificateFile {$this->certPath}/fullchain.pem|g' '{$configFile}' 2>/dev/null",
            "sudo sed -i 's|SSLCertificateKeyFile /etc/ssl/private/ssl-cert-snakeoil.key|SSLCertificateKeyFile {$this->certPath}/privkey.pem|g' '{$configFile}' 2>/dev/null"
        ];
        
        foreach ($commands as $command) {
            exec($command);
        }
        
        // Reload Apache
        exec("sudo systemctl reload apache2 2>/dev/null");
        
        echo "✅ SSL configured for {$domain}\n";
        return true;
    }
    
    /**
     * Fix SSL for all custom domains
     */
    public function fixAllDomains() {
        $domains = glob($this->apachePath . '*.loanratelk.top.conf');
        
        echo "📁 Found " . count($domains) . " custom domain(s)\n\n";
        
        $fixed = 0;
        foreach ($domains as $configFile) {
            $domain = basename($configFile, '.conf');
            if ($this->fixDomainSSL($domain)) {
                $fixed++;
            }
        }
        
        echo "\n✅ Fixed SSL for {$fixed} domain(s)\n";
        return $fixed;
    }
    
    /**
     * Test SSL for a domain
     */
    public function testDomainSSL($domain) {
        echo "🧪 Testing SSL for: {$domain}\n";
        
        exec("curl -I https://{$domain} --insecure --max-time 10 2>/dev/null", $output, $returnCode);
        
        if ($returnCode === 0 && !empty($output)) {
            echo "✅ SSL working: " . $output[0] . "\n";
            return true;
        } else {
            echo "❌ SSL not working\n";
            return false;
        }
    }
    
    /**
     * Monitor and auto-fix SSL issues
     */
    public function monitorAndFix() {
        echo "🔍 Monitoring SSL status...\n\n";
        
        $domains = glob($this->apachePath . '*.loanratelk.top.conf');
        
        foreach ($domains as $configFile) {
            $domain = basename($configFile, '.conf');
            
            if (!$this->testDomainSSL($domain)) {
                echo "🔧 Auto-fixing SSL for {$domain}...\n";
                $this->fixDomainSSL($domain);
                
                // Test again
                if ($this->testDomainSSL($domain)) {
                    echo "✅ Auto-fix successful for {$domain}\n";
                } else {
                    echo "❌ Auto-fix failed for {$domain}\n";
                }
            }
            echo "\n";
        }
    }
}

// CLI Usage
if ($argc > 1) {
    $manager = new SSLDomainManager();
    
    switch ($argv[1]) {
        case 'fix':
            if (isset($argv[2])) {
                $manager->fixDomainSSL($argv[2]);
            } else {
                $manager->fixAllDomains();
            }
            break;
            
        case 'test':
            if (isset($argv[2])) {
                $manager->testDomainSSL($argv[2]);
            }
            break;
            
        case 'monitor':
            $manager->monitorAndFix();
            break;
            
        default:
            echo "Usage:\n";
            echo "  php ssl_domain_manager.php fix [domain]     - Fix SSL for domain or all domains\n";
            echo "  php ssl_domain_manager.php test <domain>    - Test SSL for domain\n";
            echo "  php ssl_domain_manager.php monitor          - Monitor and auto-fix all domains\n";
    }
} else {
    echo "SaaS SSL Domain Manager\n";
    echo "Usage: php ssl_domain_manager.php [fix|test|monitor] [domain]\n";
}
