#!/bin/bash

# Apache SSL Automation Script
# Automatically configures SSL for any domain

DOMAIN=$1
WEBROOT="/var/www/interrandadmin.com/public"
APACHE_SITES="/etc/apache2/sites-available"
CERT_EMAIL="<EMAIL>"

if [ -z "$DOMAIN" ]; then
    echo "Usage: $0 <domain.name>"
    echo "Example: $0 test.loanratelk.top"
    exit 1
fi

echo "=== Apache SSL Automation for $DOMAIN ==="

# Step 1: Create Apache virtual host
echo "📝 Creating Apache virtual host..."

cat > "$APACHE_SITES/$DOMAIN.conf" << EOF
<VirtualHost *:80>
    ServerName $DOMAIN
    DocumentRoot $WEBROOT
    
    # Allow Let's Encrypt challenges
    <Directory "$WEBROOT/.well-known">
        Options -Indexes +FollowSymLinks
        AllowOverride None
        Require all granted
    </Directory>
    
    # Laravel application
    <Directory $WEBROOT>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog \${APACHE_LOG_DIR}/${DOMAIN}_error.log
    CustomLog \${APACHE_LOG_DIR}/${DOMAIN}_access.log combined
</VirtualHost>

<VirtualHost *:443>
    ServerName $DOMAIN
    DocumentRoot $WEBROOT
    
    # SSL Configuration
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/ssl-cert-snakeoil.pem
    SSLCertificateKeyFile /etc/ssl/private/ssl-cert-snakeoil.key
    
    # Laravel application
    <Directory $WEBROOT>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog \${APACHE_LOG_DIR}/${DOMAIN}_ssl_error.log
    CustomLog \${APACHE_LOG_DIR}/${DOMAIN}_ssl_access.log combined
</VirtualHost>
EOF

# Step 2: Enable the site
echo "🔧 Enabling Apache site..."
sudo a2ensite "$DOMAIN.conf"
sudo systemctl reload apache2

# Step 3: Create .well-known directory
echo "📁 Creating .well-known directory..."
sudo mkdir -p "$WEBROOT/.well-known/acme-challenge"
sudo chown -R www-data:www-data "$WEBROOT/.well-known"
sudo chmod -R 755 "$WEBROOT/.well-known"

# Step 4: Test domain accessibility
echo "🧪 Testing domain accessibility..."
if curl -I "http://$DOMAIN" --max-time 10 2>/dev/null | grep -q "HTTP"; then
    echo "✅ Domain is accessible via HTTP"
else
    echo "⚠️  Domain may not be accessible yet (DNS propagation)"
fi

# Step 5: Obtain SSL certificate
echo "🔒 Obtaining SSL certificate..."

# Try certbot with webroot
if sudo certbot certonly \
    --webroot \
    --webroot-path="$WEBROOT" \
    --email "$CERT_EMAIL" \
    --agree-tos \
    --no-eff-email \
    --domains "$DOMAIN" \
    --non-interactive; then
    
    echo "✅ SSL certificate obtained successfully"
    
    # Update Apache configuration with real certificate
    sudo sed -i "s|SSLCertificateFile /etc/ssl/certs/ssl-cert-snakeoil.pem|SSLCertificateFile /etc/letsencrypt/live/$DOMAIN/fullchain.pem|g" "$APACHE_SITES/$DOMAIN.conf"
    sudo sed -i "s|SSLCertificateKeyFile /etc/ssl/private/ssl-cert-snakeoil.key|SSLCertificateKeyFile /etc/letsencrypt/live/$DOMAIN/privkey.pem|g" "$APACHE_SITES/$DOMAIN.conf"
    
    # Add HTTPS redirect to HTTP virtual host
    sudo sed -i '/<\/Directory>/a\\n    # Redirect to HTTPS\n    RewriteEngine On\n    RewriteCond %{HTTPS} off\n    RewriteCond %{REQUEST_URI} !^/\\.well-known/\n    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]' "$APACHE_SITES/$DOMAIN.conf"
    
    # Reload Apache
    sudo systemctl reload apache2
    
    echo "🎉 SSL configuration complete!"
    
else
    echo "❌ SSL certificate failed, using fallback certificate..."
    
    # Use interrandadmin.com certificate as fallback
    FALLBACK_CERT="/etc/letsencrypt/live/interrandadmin.com"
    
    if [ -f "$FALLBACK_CERT/fullchain.pem" ]; then
        sudo sed -i "s|SSLCertificateFile /etc/ssl/certs/ssl-cert-snakeoil.pem|SSLCertificateFile $FALLBACK_CERT/fullchain.pem|g" "$APACHE_SITES/$DOMAIN.conf"
        sudo sed -i "s|SSLCertificateKeyFile /etc/ssl/private/ssl-cert-snakeoil.key|SSLCertificateKeyFile $FALLBACK_CERT/privkey.pem|g" "$APACHE_SITES/$DOMAIN.conf"
        
        sudo systemctl reload apache2
        
        echo "✅ Fallback SSL certificate configured"
    else
        echo "❌ No fallback certificate available"
    fi
fi

# Step 6: Test SSL
echo "🧪 Testing SSL..."
if curl -I "https://$DOMAIN" --insecure --max-time 10 2>/dev/null | grep -q "HTTP"; then
    echo "✅ HTTPS is working!"
    echo "🌐 Test URL: https://$DOMAIN"
else
    echo "⚠️  HTTPS may need a few minutes to propagate"
fi

echo ""
echo "=== Configuration Summary ==="
echo "Domain: $DOMAIN"
echo "Apache config: $APACHE_SITES/$DOMAIN.conf"
echo "Document root: $WEBROOT"
echo "SSL status: $(curl -I "https://$DOMAIN" --insecure --max-time 5 2>/dev/null | head -1 || echo "Not accessible")"
echo ""
echo "📋 Next steps:"
echo "   - Test: https://$DOMAIN"
echo "   - Check logs: sudo tail -f /var/log/apache2/${DOMAIN}_error.log"
echo "   - Renew certs: sudo certbot renew"
