#!/bin/bash

# Setup SSL Monitoring for SaaS Custom Domains
echo "=== Setting up SSL Monitoring ==="

# Create log directory
sudo mkdir -p /var/log/ssl-monitor
sudo chown www-data:www-data /var/log/ssl-monitor

# Create monitoring script
cat > /var/www/interrandadmin.com/ssl_monitor_cron.php << 'EOF'
<?php
/**
 * Cron job for SSL monitoring
 */

// Change to the correct directory
chdir('/var/www/interrandadmin.com');

// Include the SSL manager
require_once 'ssl_domain_manager.php';

// Log file
$logFile = '/var/log/ssl-monitor/ssl-monitor.log';

// Start monitoring
$timestamp = date('Y-m-d H:i:s');
file_put_contents($logFile, "\n[$timestamp] Starting SSL monitoring...\n", FILE_APPEND);

// Capture output
ob_start();

$manager = new SSLDomainManager();
$manager->monitorAndFix();

$output = ob_get_clean();

// Log the output
file_put_contents($logFile, $output . "\n", FILE_APPEND);
file_put_contents($logFile, "[$timestamp] SSL monitoring completed.\n", FILE_APPEND);

echo "SSL monitoring completed at $timestamp\n";
EOF

# Make it executable
chmod +x /var/www/interrandadmin.com/ssl_monitor_cron.php

# Add to crontab (runs every 30 minutes)
echo "Adding cron job for SSL monitoring..."

# Create cron entry
CRON_ENTRY="*/30 * * * * cd /var/www/interrandadmin.com && php ssl_monitor_cron.php >> /var/log/ssl-monitor/cron.log 2>&1"

# Add to crontab
(crontab -l 2>/dev/null | grep -v "ssl_monitor_cron.php"; echo "$CRON_ENTRY") | crontab -

echo "✅ SSL monitoring setup complete!"
echo ""
echo "📋 What was configured:"
echo "   - SSL monitoring script: /var/www/interrandadmin.com/ssl_monitor_cron.php"
echo "   - Log directory: /var/log/ssl-monitor/"
echo "   - Cron job: Every 30 minutes"
echo ""
echo "📝 Manual commands:"
echo "   - Fix all domains: php ssl_domain_manager.php fix"
echo "   - Test domain: php ssl_domain_manager.php test domain.name"
echo "   - Monitor now: php ssl_domain_manager.php monitor"
echo ""
echo "📊 View logs:"
echo "   - SSL monitor: tail -f /var/log/ssl-monitor/ssl-monitor.log"
echo "   - Cron output: tail -f /var/log/ssl-monitor/cron.log"
