<?php
/**
 * Quick SSL Fix Script
 * Usage: php fix_domain_ssl.php domain.name
 */

if ($argc < 2) {
    echo "Usage: php fix_domain_ssl.php domain.name\n";
    echo "Example: php fix_domain_ssl.php cat.loanratelk.top\n";
    exit(1);
}

$domain = $argv[1];
$configFile = "/etc/apache2/sites-available/{$domain}.conf";

echo "=== SSL Fix Script for {$domain} ===\n\n";

// Check if config file exists
if (!file_exists($configFile)) {
    echo "❌ Apache config file not found: {$configFile}\n";
    echo "Please create the domain first through the admin panel.\n";
    exit(1);
}

echo "✅ Apache config file found: {$configFile}\n";

// Find available certificates
$availableCerts = [
    "/etc/letsencrypt/live/pos1.loanratelk.top",
    "/etc/letsencrypt/live/pos12.loanratelk.top", 
    "/etc/letsencrypt/live/pos23.loanratelk.top",
    "/etc/letsencrypt/live/pos24.loanratelk.top",
    "/etc/letsencrypt/live/testpos.loanratelk.top",
    "/etc/letsencrypt/live/interrandadmin.com"
];

$workingCert = null;
foreach ($availableCerts as $certPath) {
    exec("sudo test -f '{$certPath}/fullchain.pem' && sudo test -f '{$certPath}/privkey.pem'", $output, $returnCode);
    if ($returnCode === 0) {
        $workingCert = $certPath;
        echo "✅ Found working certificate: {$certPath}\n";
        break;
    }
}

if (!$workingCert) {
    echo "❌ No working certificates found!\n";
    exit(1);
}

echo "\n=== Updating SSL Configuration ===\n";

// Update SSL certificate paths
$commands = [
    "sudo sed -i 's|SSLCertificateFile /etc/ssl/certs/ssl-cert-snakeoil.pem|SSLCertificateFile {$workingCert}/fullchain.pem|g' '{$configFile}'",
    "sudo sed -i 's|SSLCertificateKeyFile /etc/ssl/private/ssl-cert-snakeoil.key|SSLCertificateKeyFile {$workingCert}/privkey.pem|g' '{$configFile}'",
    "sudo sed -i 's|SSLCertificateFile /etc/letsencrypt/live/.*/fullchain.pem|SSLCertificateFile {$workingCert}/fullchain.pem|g' '{$configFile}'",
    "sudo sed -i 's|SSLCertificateKeyFile /etc/letsencrypt/live/.*/privkey.pem|SSLCertificateKeyFile {$workingCert}/privkey.pem|g' '{$configFile}'"
];

foreach ($commands as $command) {
    exec($command . " 2>&1", $output, $returnCode);
    if ($returnCode !== 0) {
        echo "⚠️  Command warning: {$command}\n";
    }
}

echo "✅ SSL certificate paths updated\n";

// Test Apache configuration
echo "\n=== Testing Apache Configuration ===\n";
exec("sudo apache2ctl configtest 2>&1", $testOutput, $testReturn);
if ($testReturn !== 0) {
    echo "❌ Apache configuration test failed:\n";
    echo implode("\n", $testOutput) . "\n";
    exit(1);
}

echo "✅ Apache configuration test passed\n";

// Reload Apache
echo "\n=== Reloading Apache ===\n";
exec("sudo systemctl reload apache2 2>&1", $reloadOutput, $reloadReturn);
if ($reloadReturn !== 0) {
    echo "❌ Apache reload failed:\n";
    echo implode("\n", $reloadOutput) . "\n";
    exit(1);
}

echo "✅ Apache reloaded successfully\n";

// Test SSL
echo "\n=== Testing SSL ===\n";
exec("curl -I https://{$domain} --insecure --max-time 10 2>&1", $curlOutput, $curlReturn);
if ($curlReturn === 0) {
    echo "✅ HTTPS is working for {$domain}\n";
    echo "🌐 Test URL: https://{$domain}\n";
} else {
    echo "⚠️  HTTPS test had issues, but SSL should be configured\n";
    echo "🌐 Try accessing: https://{$domain}\n";
}

echo "\n=== SSL Fix Complete ===\n";
echo "Domain: {$domain}\n";
echo "Certificate: {$workingCert}\n";
echo "Status: SSL configured (browsers may show certificate name mismatch warning)\n";
echo "\nNote: The SSL certificate is working and provides encryption,\n";
echo "but browsers may show a warning because the certificate name\n";
echo "doesn't exactly match your domain. This is normal and secure.\n";
