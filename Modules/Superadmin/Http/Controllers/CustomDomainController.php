<?php

namespace Modules\Superadmin\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\RateLimiter;
use Modules\Superadmin\Entities\CustomDomain;
use Modules\Superadmin\Entities\Subscription;
use Modules\Superadmin\Http\Requests\CustomDomainRequest;
use Yajra\DataTables\Facades\DataTables;

class CustomDomainController extends Controller
{
    /**
     * Display a listing of custom domains for the business.
     */
    public function index()
    {
        // Determine if this is a superadmin or business user access
        $isSuperadmin = $this->isSuperadminAccess();
        $business_id = request()->session()->get('user.business_id');

        // For superadmin, they can view all domains or filter by business
        if ($isSuperadmin) {
            // Superadmin needs business_settings.access permission
            if (!auth()->user()->can('business_settings.access')) {
                abort(403, 'Unauthorized action.');
            }
            // Superadmin can see all domains or filter by business
            // Only use business_id if explicitly provided in request
            $business_id = request()->has('business_id') ? request()->get('business_id') : null;
        } else {
            // For business users, check subscription-based custom domain permission
            if (!CustomDomain::businessHasPermission($business_id)) {
                $subscription = \Modules\Superadmin\Entities\Subscription::active_subscription($business_id);

                if (!$subscription) {
                    $errorMessage = 'Custom Domain feature requires an active subscription. Please subscribe to a plan that includes custom domains.';
                } elseif (!CustomDomain::isPaidSubscription($subscription)) {
                    $errorMessage = 'Custom Domain feature is only available with paid subscriptions. Please upgrade to a paid plan to use custom domains.';
                } else {
                    $errorMessage = 'Custom Domain feature is not included in your current subscription plan. Please upgrade to a plan that includes custom domains.';
                }

                return redirect()->route('business.getBusinessSettings')
                    ->with('error', $errorMessage);
            }
        }

        if (request()->ajax()) {
            $domainsQuery = CustomDomain::query();

            if ($isSuperadmin && !request()->has('business_id')) {
                // Superadmin sees all domains with business info
                $domainsQuery->with('business');
            } else {
                // Business user or filtered superadmin view
                $domainsQuery->where('business_id', $business_id);
            }

            $domains = $domainsQuery->orderBy('created_at', 'desc');

            return DataTables::of($domains)
                ->addColumn('business_name', function ($domain) use ($isSuperadmin) {
                    if ($isSuperadmin && $domain->business) {
                        return '<span class="badge badge-info">' . $domain->business->name . '</span>';
                    }
                    return '';
                })
                ->addColumn('status', function ($domain) {
                    return $domain->getStatusBadge();
                })
                ->addColumn('ssl_status', function ($domain) {
                    return $domain->getSslStatusBadge();
                })
                ->addColumn('actions', function ($domain) use ($isSuperadmin) {
                    $actions = '';

                    if (!$domain->domain_verified) {
                        $routePrefix = $isSuperadmin ? 'superadmin' : 'business';
                        $actions .= '<button class="btn btn-sm modern-btn modern-btn-primary auto-configure" data-id="' . $domain->id . '" data-route-prefix="' . $routePrefix . '" title="Auto Setup with SSL" style="margin-right: 5px;">
                            <i class="fas fa-magic"></i> Auto Setup
                        </button>';
                    }

                    // Add Fix Status button for domains with SSL active but still pending
                    if ($domain->ssl_status === 'active' && !$domain->domain_verified) {
                        $actions .= '<button class="btn btn-sm btn-warning fix-status" data-id="' . $domain->id . '" title="Fix Domain Status" style="margin-right: 5px;">
                            <i class="fas fa-wrench"></i> Fix Status
                        </button>';
                    }

                    $actions .= '<button class="btn btn-sm btn-info check-status" data-id="' . $domain->id . '" title="Check Status" style="margin-right: 5px;">
                        <i class="fas fa-sync"></i>
                    </button>';

                    $actions .= '<button class="btn btn-sm btn-warning view-instructions" data-id="' . $domain->id . '" title="DNS Instructions" style="margin-right: 5px;">
                        <i class="fas fa-info-circle"></i>
                    </button>';

                    if ($isSuperadmin) {
                        $actions .= '<button class="btn btn-sm btn-success verify-domain" data-id="' . $domain->id . '" title="Verify Domain" style="margin-right: 5px;">
                            <i class="fas fa-check-circle"></i>
                        </button>';

                        $actions .= '<button class="btn btn-sm btn-primary issue-ssl" data-id="' . $domain->id . '" title="Issue SSL" style="margin-right: 5px;">
                            <i class="fas fa-lock"></i>
                        </button>';
                    }

                    $actions .= '<button class="btn btn-sm btn-danger delete-domain" data-id="' . $domain->id . '" title="Delete Domain">
                        <i class="fas fa-trash"></i>
                    </button>';

                    return $actions;
                })
                ->rawColumns(['business_name', 'status', 'ssl_status', 'actions'])
                ->make(true);
        }

        // For superadmin, show global statistics unless filtering by specific business
        if ($isSuperadmin && !$business_id) {
            $maxDomains = 'Unlimited';
            $currentCount = CustomDomain::count(); // All domains across all businesses
            $canAddMore = true;
        } else {
            // For business users or filtered superadmin view
            $actualBusinessId = $business_id ?: request()->session()->get('user.business_id');
            $maxDomains = CustomDomain::getMaxDomainsForBusiness($actualBusinessId);
            $currentCount = CustomDomain::where('business_id', $actualBusinessId)->count();
            $canAddMore = CustomDomain::canAddMoreDomains($actualBusinessId);
        }

        // Get real statistics
        $stats = CustomDomain::getStatistics($business_id, $isSuperadmin);

        // Get subscription status for business users
        $subscriptionStatus = null;
        if (!$isSuperadmin) {
            $subscriptionStatus = CustomDomain::getSubscriptionStatus($business_id);
        }

        return view('superadmin::custom-domains.index', compact(
            'maxDomains',
            'currentCount',
            'canAddMore',
            'isSuperadmin',
            'stats',
            'subscriptionStatus'
        ));
    }

    /**
     * Determine if the current request is from superadmin access
     */
    private function isSuperadminAccess()
    {
        $route = request()->route();
        $routeName = $route ? $route->getName() : null;
        $path = request()->path();

        // Check route name first
        if ($routeName && str_starts_with($routeName, 'superadmin.')) {
            return true;
        }

        // Check URL path as fallback
        if (str_starts_with($path, 'superadmin/')) {
            return true;
        }

        // Check if user has superadmin middleware access
        $administrator_list = config('constants.administrator_usernames');
        if (!empty(auth()->user()) && in_array(strtolower(auth()->user()->username), explode(',', strtolower($administrator_list)))) {
            return str_contains($path, 'superadmin') || str_contains($routeName ?? '', 'superadmin');
        }

        return false;
    }

    /**
     * Check if user has permission to access custom domains.
     */
    private function hasCustomDomainPermission()
    {
        $isSuperadmin = $this->isSuperadminAccess();
        $business_id = request()->session()->get('user.business_id');

        if ($isSuperadmin) {
            // Superadmin needs business_settings.access permission
            return auth()->user()->can('business_settings.access');
        } else {
            // Business users need subscription-based permission
            return CustomDomain::businessHasPermission($business_id);
        }
    }

    /**
     * Store a newly created custom domain.
     */
    public function store(CustomDomainRequest $request)
    {
        if (!$this->hasCustomDomainPermission()) {
            abort(403, 'Unauthorized action.');
        }

        $business_id = request()->session()->get('user.business_id');

        try {
            DB::beginTransaction();

            $domain = CustomDomain::create([
                'business_id' => $business_id,
                'custom_domain' => strtolower($request->custom_domain),
                'verification_method' => 'dns_txt'
            ]);

            $domain->generateVerificationToken();

            DB::commit();

            return response()->json([
                'success' => true, 
                'message' => 'Custom domain added successfully. Please configure DNS records to verify ownership.',
                'domain' => $domain
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => 'Error adding custom domain: ' . $e->getMessage()]);
        }
    }

    /**
     * Auto-configure domain (simplified one-click setup).
     */
    public function autoConfigureDomain(Request $request, $id)
    {
        if (!$this->hasCustomDomainPermission()) {
            abort(403, 'Unauthorized action.');
        }

        $business_id = request()->session()->get('user.business_id');
        $domain = CustomDomain::where('id', $id)->where('business_id', $business_id)->first();

        if (!$domain) {
            return response()->json(['success' => false, 'message' => 'Domain not found.']);
        }

        // Rate limiting
        $key = 'domain_config_' . $business_id . '_' . $id;
        if (RateLimiter::tooManyAttempts($key, 3)) {
            $seconds = RateLimiter::availableIn($key);
            return response()->json([
                'success' => false,
                'message' => "Too many configuration attempts. Please try again in {$seconds} seconds."
            ]);
        }

        RateLimiter::hit($key, 600); // 10 minutes window

        try {
            \Log::info("Auto-configure request for domain ID: {$id}, Domain: {$domain->custom_domain}");

            $apacheService = new \Modules\Superadmin\Services\ApacheConfigService();
            $result = $apacheService->autoConfigureDomain($domain);

            \Log::info("Auto-configure result for {$domain->custom_domain}: " . json_encode($result));

            return response()->json($result);

        } catch (\Exception $e) {
            \Log::error("Auto-configure exception for {$domain->custom_domain}: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Configuration failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Check domain status.
     */
    public function checkStatus(Request $request, $id)
    {
        if (!$this->hasCustomDomainPermission()) {
            abort(403, 'Unauthorized action.');
        }

        $business_id = request()->session()->get('user.business_id');
        $domain = CustomDomain::where('id', $id)->where('business_id', $business_id)->first();

        if (!$domain) {
            return response()->json(['success' => false, 'message' => 'Domain not found.']);
        }

        try {
            $apacheService = new \Modules\Superadmin\Services\ApacheConfigService();
            $status = $apacheService->checkDomainPointing($domain->custom_domain);

            return response()->json([
                'success' => true,
                'domain' => $domain->custom_domain,
                'status' => $status,
                'ssl_status' => $domain->ssl_status,
                'verified' => $domain->domain_verified
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Status check failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get DNS instructions for a domain.
     */
    public function getDnsInstructions($id)
    {
        if (!$this->hasCustomDomainPermission()) {
            abort(403, 'Unauthorized action.');
        }

        $business_id = request()->session()->get('user.business_id');
        $domain = CustomDomain::where('id', $id)->where('business_id', $business_id)->first();

        if (!$domain) {
            return response()->json(['success' => false, 'message' => 'Domain not found.']);
        }

        try {
            $apacheService = new \Modules\Superadmin\Services\ApacheConfigService();
            $instructions = $apacheService->getDnsInstructions($domain->custom_domain);

            return response()->json([
                'success' => true,
                'domain' => $domain->custom_domain,
                'instructions' => $instructions
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get DNS instructions: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Verify domain (Superadmin only).
     */
    public function verify(Request $request, $id)
    {
        if (!$this->hasCustomDomainPermission()) {
            abort(403, 'Unauthorized action.');
        }

        $domain = CustomDomain::find($id);

        if (!$domain) {
            return response()->json(['success' => false, 'message' => 'Domain not found.']);
        }

        try {
            $verificationResult = $domain->verifyDomainOwnership();

            if ($verificationResult) {
                $domain->domain_verified = true;
                $domain->verified_at = now();
                $domain->save();

                return response()->json([
                    'success' => true,
                    'message' => 'Domain verified successfully!'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Domain verification failed. Please check DNS configuration.'
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Verification error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Issue SSL certificate for verified domain.
     */
    public function issueSSL(Request $request, $id)
    {
        if (!$this->hasCustomDomainPermission()) {
            abort(403, 'Unauthorized action.');
        }

        $business_id = request()->session()->get('user.business_id');
        $domain = CustomDomain::where('id', $id)->where('business_id', $business_id)->first();

        if (!$domain) {
            return response()->json(['success' => false, 'message' => 'Domain not found.']);
        }

        if (!$domain->domain_verified) {
            return response()->json(['success' => false, 'message' => 'Domain must be verified before issuing SSL certificate.']);
        }

        try {
            $sslService = new \Modules\Superadmin\Services\SSLCertificateService();
            $success = $sslService->issueCertificate($domain);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'SSL certificate issued successfully! Your domain is now secure.'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'SSL certificate issuance failed. Please try again later.'
                ]);
            }

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Error issuing SSL certificate: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified custom domain and clean up all configurations.
     */
    public function destroy($id)
    {
        if (!$this->hasCustomDomainPermission()) {
            abort(403, 'Unauthorized action.');
        }

        $business_id = request()->session()->get('user.business_id');
        $domain = CustomDomain::where('id', $id)->where('business_id', $business_id)->first();

        if (!$domain) {
            return response()->json(['success' => false, 'message' => 'Domain not found.']);
        }

        try {
            $domainName = $domain->custom_domain;

            // Step 1: Remove Apache configuration
            $apacheService = new \Modules\Superadmin\Services\ApacheConfigService();
            $apacheRemoved = $apacheService->removeDomainConfig($domain);

            // Step 2: Remove SSL certificates
            $sslService = new \Modules\Superadmin\Services\SSLCertificateService();
            $sslRemoved = $sslService->revokeCertificate($domain);

            // Step 3: Clear domain cache
            \Modules\Superadmin\Http\Middleware\CustomDomainResolver::clearDomainCache($domainName);

            // Step 4: Delete from database
            $domain->delete();

            \Log::info("Domain {$domainName} completely removed - Apache: " . ($apacheRemoved ? 'Yes' : 'No') . ", SSL: " . ($sslRemoved ? 'Yes' : 'No'));

            return response()->json([
                'success' => true,
                'message' => 'Custom domain and all configurations deleted successfully.',
                'details' => [
                    'domain' => $domainName,
                    'apache_removed' => $apacheRemoved,
                    'ssl_removed' => $sslRemoved
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error("Error deleting domain {$domain->custom_domain}: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error deleting domain: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get domain statistics via AJAX
     */
    public function getStats()
    {
        if (!$this->hasCustomDomainPermission()) {
            abort(403, 'Unauthorized action.');
        }

        $isSuperadmin = $this->isSuperadminAccess();
        $business_id = request()->session()->get('user.business_id');

        $stats = CustomDomain::getStatistics($business_id, $isSuperadmin);

        return response()->json([
            'success' => true,
            'stats' => $stats
        ]);
    }

    /**
     * Fix domain status (for domains stuck in pending)
     */
    public function fixDomainStatus(Request $request, $id)
    {
        if (!$this->hasCustomDomainPermission()) {
            abort(403, 'Unauthorized action.');
        }

        $isSuperadmin = $this->isSuperadminAccess();
        $business_id = request()->session()->get('user.business_id');

        $query = CustomDomain::where('id', $id);
        if (!$isSuperadmin) {
            $query->where('business_id', $business_id);
        }

        $domain = $query->first();

        if (!$domain) {
            return response()->json(['success' => false, 'message' => 'Domain not found.']);
        }

        try {
            // Check if domain has SSL active but is still showing as pending
            if ($domain->ssl_status === 'active' && !$domain->domain_verified) {
                $domain->domain_verified = true;
                $domain->is_active = true;
                $domain->verified_at = now();
                $domain->save();

                Log::info("Fixed domain status for: {$domain->custom_domain}");

                return response()->json([
                    'success' => true,
                    'message' => 'Domain status fixed successfully!',
                    'domain' => $domain->fresh()
                ]);
            }

            // Try to verify domain again
            $verificationResult = $domain->verifyDomainOwnership();

            if ($verificationResult) {
                $domain->domain_verified = true;
                $domain->is_active = true;
                $domain->verified_at = now();
                $domain->save();

                return response()->json([
                    'success' => true,
                    'message' => 'Domain verified and activated successfully!'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Domain verification still failing. Please check DNS configuration.'
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fixing domain status: ' . $e->getMessage()
            ]);
        }
    }
}
