<?php

namespace Modules\Superadmin\Entities;

use App\Business;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class CustomDomain extends Model
{
    protected $guarded = ['id'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'domain_verified' => 'boolean',
        'is_active' => 'boolean',
        'verified_at' => 'datetime',
        'ssl_issued_at' => 'datetime',
        'ssl_expires_at' => 'datetime',
        'last_verification_attempt' => 'datetime',
    ];

    /**
     * Get the business that owns the custom domain.
     */
    public function business()
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Scope a query to only include verified domains.
     */
    public function scopeVerified($query)
    {
        return $query->where('domain_verified', true);
    }

    /**
     * Scope a query to only include active domains.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include domains with active SSL.
     */
    public function scopeSslActive($query)
    {
        return $query->where('ssl_status', 'active');
    }

    /**
     * Generate a unique verification token.
     */
    public function generateVerificationToken()
    {
        $this->verification_token = Str::random(32);
        $this->save();
        return $this->verification_token;
    }

    /**
     * Check if domain is verified.
     */
    public function isVerified()
    {
        return $this->domain_verified;
    }

    /**
     * Check if SSL is active.
     */
    public function hasSslActive()
    {
        return $this->ssl_status === 'active';
    }

    /**
     * Check if SSL is expired or expiring soon.
     */
    public function isSslExpiring($days = 30)
    {
        if (!$this->ssl_expires_at) {
            return false;
        }
        
        return $this->ssl_expires_at->diffInDays(now()) <= $days;
    }

    /**
     * Get DNS instructions for domain verification.
     */
    public function getDnsInstructions()
    {
        $main_domain = 'interrandadmin.com'; // Use your actual domain
        
        return [
            'cname' => [
                'type' => 'CNAME',
                'name' => $this->custom_domain,
                'value' => $main_domain,
                'ttl' => '300'
            ],
            'txt_verification' => [
                'type' => 'TXT',
                'name' => '_domain-verification.' . $this->custom_domain,
                'value' => $this->verification_token,
                'ttl' => '300'
            ]
        ];
    }

    /**
     * Verify domain ownership using the verification service.
     */
    public function verifyDomainOwnership()
    {
        if (!$this->verification_token) {
            $this->generateVerificationToken();
        }

        $verificationService = new \Modules\Superadmin\Services\DomainVerificationService();
        return $verificationService->verifyDomainOwnership($this);
    }

    /**
     * Get the status badge HTML for display.
     */
    public function getStatusBadge()
    {
        if ($this->domain_verified) {
            return '<span class="status-badge status-verified"><i class="fas fa-check-circle"></i> Verified</span>';
        } else {
            return '<span class="status-badge status-pending"><i class="fas fa-clock"></i> Pending</span>';
        }
    }

    /**
     * Get SSL status badge HTML for display.
     */
    public function getSslStatusBadge()
    {
        switch ($this->ssl_status) {
            case 'active':
                return '<span class="status-badge ssl-active"><i class="fas fa-lock"></i> Active</span>';
            case 'pending':
                return '<span class="status-badge ssl-pending"><i class="fas fa-clock"></i> Pending</span>';
            case 'failed':
                return '<span class="status-badge status-pending"><i class="fas fa-times"></i> Failed</span>';
            default:
                return '<span class="status-badge ssl-pending"><i class="fas fa-minus"></i> None</span>';
        }
    }

    /**
     * Check if business has permission to use custom domains.
     * Only businesses with PAID subscriptions can use custom domains.
     */
    public static function businessHasPermission($business_id)
    {
        $subscription = Subscription::active_subscription($business_id);

        if (!$subscription) {
            return false;
        }

        // Check if this is a PAID subscription
        if (!self::isPaidSubscription($subscription)) {
            return false;
        }

        $package_details = $subscription->package_details;

        // Check both possible locations for custom_domain permission
        return (isset($package_details['custom_permissions']['custom_domain']) &&
                $package_details['custom_permissions']['custom_domain'] == 1) ||
               (isset($package_details['custom_domain']) &&
                $package_details['custom_domain'] == 1);
    }

    /**
     * Check if a subscription is a paid subscription (not free/trial).
     */
    public static function isPaidSubscription($subscription)
    {
        // Check if package price is greater than 0
        if ($subscription->package_price > 0) {
            return true;
        }

        // Check if original price is greater than 0 (in case of discounts)
        if ($subscription->original_price > 0) {
            return true;
        }

        // Check if there's a valid payment transaction ID (not just a placeholder)
        if (!empty($subscription->payment_transaction_id) &&
            $subscription->payment_transaction_id !== '123' &&
            $subscription->payment_transaction_id !== 'free' &&
            $subscription->payment_transaction_id !== 'trial') {
            return true;
        }

        // Check if paid via actual payment gateway (not offline with 0 price)
        $paidGateways = ['stripe', 'paypal', 'razorpay', 'pesapal', 'flutterwave', 'paystack', 'myfatoorsh'];
        if (in_array($subscription->paid_via, $paidGateways)) {
            return true;
        }

        // If offline payment but with actual price, consider it paid
        if ($subscription->paid_via === 'offline' && $subscription->package_price > 0) {
            return true;
        }

        return false;
    }

    /**
     * Get subscription status and custom domain availability info.
     */
    public static function getSubscriptionStatus($business_id)
    {
        $subscription = Subscription::active_subscription($business_id);

        if (!$subscription) {
            return [
                'has_subscription' => false,
                'is_paid' => false,
                'has_custom_domain_permission' => false,
                'message' => 'No active subscription found. Please subscribe to a plan that includes custom domains.',
                'action_needed' => 'subscribe'
            ];
        }

        $isPaid = self::isPaidSubscription($subscription);
        $hasPermission = self::businessHasPermission($business_id);

        if (!$isPaid) {
            return [
                'has_subscription' => true,
                'is_paid' => false,
                'has_custom_domain_permission' => false,
                'message' => 'Custom domains are only available with paid subscriptions. Please upgrade to a paid plan.',
                'action_needed' => 'upgrade_to_paid',
                'subscription' => $subscription
            ];
        }

        if (!$hasPermission) {
            return [
                'has_subscription' => true,
                'is_paid' => true,
                'has_custom_domain_permission' => false,
                'message' => 'Your current plan does not include custom domains. Please upgrade to a plan with custom domain support.',
                'action_needed' => 'upgrade_plan',
                'subscription' => $subscription
            ];
        }

        return [
            'has_subscription' => true,
            'is_paid' => true,
            'has_custom_domain_permission' => true,
            'message' => 'Custom domains are available in your subscription.',
            'action_needed' => 'none',
            'subscription' => $subscription
        ];
    }

    /**
     * Get the maximum allowed custom domains for a business.
     */
    public static function getMaxDomainsForBusiness($business_id)
    {
        $subscription = Subscription::active_subscription($business_id);

        if (!$subscription) {
            return 0;
        }

        $package_details = $subscription->package_details;

        // Check both possible locations for max_custom_domains
        return $package_details['custom_permissions']['max_custom_domains'] ??
               $package_details['max_custom_domains'] ?? 1;
    }

    /**
     * Check if business can add more custom domains.
     */
    public static function canAddMoreDomains($business_id)
    {
        $maxDomains = self::getMaxDomainsForBusiness($business_id);
        $currentCount = self::where('business_id', $business_id)->count();

        return $currentCount < $maxDomains;
    }

    /**
     * Get domain statistics for a business or all businesses (for superadmin).
     */
    public static function getStatistics($business_id = null, $isSuperadmin = false)
    {
        $baseQuery = self::query();

        if (!$isSuperadmin && $business_id) {
            $baseQuery->where('business_id', $business_id);
        }

        $totalDomains = (clone $baseQuery)->count();

        // Active domains: either domain_verified=true OR (ssl_status=active AND domain_verified=true)
        $activeDomains = (clone $baseQuery)->where(function($query) {
            $query->where('domain_verified', true)
                  ->where('is_active', true);
        })->count();

        // SSL secured domains
        $sslSecuredDomains = (clone $baseQuery)->where('ssl_status', 'active')->count();

        // Pending domains: not verified yet
        $pendingDomains = (clone $baseQuery)->where('domain_verified', false)->count();

        $stats = [
            'total' => $totalDomains,
            'active' => $activeDomains,
            'ssl_secured' => $sslSecuredDomains,
            'pending' => $pendingDomains
        ];

        // For business users, add remaining count
        if (!$isSuperadmin && $business_id) {
            $maxDomains = self::getMaxDomainsForBusiness($business_id);
            $stats['remaining'] = max(0, $maxDomains - $totalDomains);
        }

        return $stats;
    }

}
