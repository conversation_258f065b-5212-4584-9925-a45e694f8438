<?php

namespace Modules\Superadmin\Services;

use Modules\Superadmin\Entities\CustomDomain;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;

class ApacheConfigService
{
    private $vhostPath;
    private $sslPath;
    private $mainDomain;

    public function __construct()
    {
        $this->vhostPath = '/etc/apache2/sites-available/';
        $this->sslPath = '/etc/ssl/certs/';
        $this->mainDomain = 'interrandadmin.com';
    }

    /**
     * Create Apache virtual host for custom domain
     */
    public function createVirtualHost(CustomDomain $domain)
    {
        try {
            $domainName = $domain->custom_domain;
            $configContent = $this->generateVhostConfig($domainName);
            
            // Write config file
            $configFile = $this->vhostPath . $domainName . '.conf';
            File::put($configFile, $configContent);
            
            // Enable site
            $this->enableSite($domainName);
            
            // Reload Apache
            $this->reloadApache();
            
            Log::info("Virtual host created for: {$domainName}");
            return true;
            
        } catch (\Exception $e) {
            Log::error("Failed to create virtual host for {$domain->custom_domain}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate Apache virtual host configuration
     */
    private function generateVhostConfig($domainName)
    {
        $documentRoot = base_path('public');
        
        return "
<VirtualHost *:80>
    ServerName {$domainName}
    ServerAlias www.{$domainName}
    DocumentRoot {$documentRoot}
    
    # Redirect to HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    ErrorLog \${APACHE_LOG_DIR}/{$domainName}_error.log
    CustomLog \${APACHE_LOG_DIR}/{$domainName}_access.log combined
</VirtualHost>

<VirtualHost *:443>
    ServerName {$domainName}
    ServerAlias www.{$domainName}
    DocumentRoot {$documentRoot}
    
    # SSL Configuration
    SSLEngine on
    SSLCertificateFile {$this->sslPath}{$domainName}/fullchain.pem
    SSLCertificateKeyFile {$this->sslPath}{$domainName}/privkey.pem
    
    # PHP Configuration - Use Apache PHP module (not PHP-FPM)
    # PHP is handled by Apache mod_php automatically
    
    # Laravel Configuration
    <Directory {$documentRoot}>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        # Laravel Pretty URLs
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteRule ^ index.php [L]
    </Directory>
    
    # Security Headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection \"1; mode=block\"
    Header always set Strict-Transport-Security \"max-age=63072000; includeSubDomains; preload\"
    
    ErrorLog \${APACHE_LOG_DIR}/{$domainName}_ssl_error.log
    CustomLog \${APACHE_LOG_DIR}/{$domainName}_ssl_access.log combined
</VirtualHost>";
    }

    /**
     * Get PHP version for configuration
     */
    private function getPhpVersion()
    {
        $version = PHP_VERSION;
        $majorMinor = implode('.', array_slice(explode('.', $version), 0, 2));
        return $majorMinor;
    }

    /**
     * Enable Apache site
     */
    private function enableSite($domainName)
    {
        $configFile = $this->vhostPath . $domainName . '.conf';

        // Check if config file exists before enabling
        if (!File::exists($configFile)) {
            throw new \Exception("Configuration file does not exist: {$configFile}");
        }

        Log::info("Enabling Apache site: {$domainName}.conf");
        exec("sudo a2ensite {$domainName}.conf 2>&1", $output, $returnCode);

        if ($returnCode !== 0) {
            Log::error("Failed to enable site {$domainName}: " . implode("\n", $output));
            throw new \Exception("Failed to enable site: " . implode("\n", $output));
        }

        Log::info("Apache site enabled successfully: {$domainName}");
    }

    /**
     * Disable Apache site
     */
    public function disableSite($domainName)
    {
        try {
            exec("a2dissite {$domainName}.conf 2>&1", $output, $returnCode);
            
            if ($returnCode === 0) {
                $this->reloadApache();
                
                // Remove config file
                $configFile = $this->vhostPath . $domainName . '.conf';
                if (File::exists($configFile)) {
                    File::delete($configFile);
                }
                
                Log::info("Virtual host disabled for: {$domainName}");
                return true;
            }
            
            return false;
            
        } catch (\Exception $e) {
            Log::error("Failed to disable site {$domainName}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Reload Apache configuration
     */
    private function reloadApache()
    {
        exec("sudo systemctl reload apache2 2>&1", $output, $returnCode);

        if ($returnCode !== 0) {
            throw new \Exception("Failed to reload Apache: " . implode("\n", $output));
        }
    }

    /**
     * Check if domain is pointing to our server
     */
    public function checkDomainPointing($domainName)
    {
        try {
            // Get our server IP
            $serverIp = $this->getServerIp();
            Log::info("Checking domain {$domainName} - Server IP: {$serverIp}");

            // Get domain IP with multiple methods
            $domainIp = $this->resolveDomainIp($domainName);
            Log::info("Domain {$domainName} resolves to: {$domainIp}");

            // Check if domain points to our server
            if ($domainIp === $serverIp || $domainIp === $domainName) {
                // If gethostbyname returns the domain name, it means DNS resolution failed
                if ($domainIp === $domainName) {
                    return [
                        'status' => 'error',
                        'pointing' => false,
                        'server_ip' => $serverIp,
                        'domain_ip' => 'DNS resolution failed',
                        'message' => "DNS resolution failed for {$domainName}. Please check if DNS records are configured."
                    ];
                }

                return [
                    'status' => 'success',
                    'pointing' => true,
                    'server_ip' => $serverIp,
                    'domain_ip' => $domainIp,
                    'message' => "Domain is correctly pointing to our server!"
                ];
            }

            return [
                'status' => 'error',
                'pointing' => false,
                'server_ip' => $serverIp,
                'domain_ip' => $domainIp,
                'message' => "Domain is pointing to {$domainIp}, but should point to {$serverIp}"
            ];

        } catch (\Exception $e) {
            Log::error("Domain pointing check failed for {$domainName}: " . $e->getMessage());
            return [
                'status' => 'error',
                'pointing' => false,
                'message' => 'DNS check failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Resolve domain IP with multiple methods
     */
    private function resolveDomainIp($domainName)
    {
        // Method 1: PHP gethostbyname
        $ip = gethostbyname($domainName);
        if ($ip !== $domainName && filter_var($ip, FILTER_VALIDATE_IP)) {
            return $ip;
        }

        // Method 2: dig command
        try {
            $output = shell_exec("dig +short {$domainName} A");
            if ($output) {
                $lines = array_filter(explode("\n", trim($output)));
                foreach ($lines as $line) {
                    $line = trim($line);
                    if (filter_var($line, FILTER_VALIDATE_IP)) {
                        return $line;
                    }
                }
            }
        } catch (\Exception $e) {
            Log::warning("dig command failed for {$domainName}: " . $e->getMessage());
        }

        // Method 3: nslookup
        try {
            $output = shell_exec("nslookup {$domainName} 2>/dev/null | grep 'Address:' | tail -1 | awk '{print \$2}'");
            if ($output) {
                $ip = trim($output);
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        } catch (\Exception $e) {
            Log::warning("nslookup command failed for {$domainName}: " . $e->getMessage());
        }

        // Return original domain name if all methods fail
        return $domainName;
    }

    /**
     * Get server IP address
     */
    private function getServerIp()
    {
        // Try multiple methods to get server IP
        $methods = [
            'curl -s https://ipinfo.io/ip',
            'curl -s https://api.ipify.org',
            'curl -s https://checkip.amazonaws.com',
            'dig +short myip.opendns.com @resolver1.opendns.com'
        ];
        
        foreach ($methods as $method) {
            $ip = trim(shell_exec($method));
            if (filter_var($ip, FILTER_VALIDATE_IP)) {
                return $ip;
            }
        }
        
        // Fallback to local IP
        return gethostbyname($this->mainDomain);
    }

    /**
     * Auto-configure domain with automatic SSL
     */
    public function autoConfigureDomain(CustomDomain $domain)
    {
        try {
            Log::info("Starting auto-configuration with SSL for domain: {$domain->custom_domain}");

            // Step 1: Check if domain is pointing to our server
            $pointingCheck = $this->checkDomainPointing($domain->custom_domain);

            if (!$pointingCheck['pointing']) {
                Log::info("Domain {$domain->custom_domain} not pointing to server yet");
                return [
                    'success' => false,
                    'step' => 'dns_check',
                    'message' => 'Domain is not pointing to our server yet. Please configure DNS first.',
                    'instructions' => $this->getDnsInstructions($domain->custom_domain),
                    'details' => $pointingCheck
                ];
            }

            Log::info("Domain {$domain->custom_domain} is pointing correctly");

            // Step 2: Create Apache virtual host with SSL
            if (!$this->createVirtualHostWithSSL($domain)) {
                return [
                    'success' => false,
                    'step' => 'vhost_creation',
                    'message' => 'Failed to create Apache virtual host configuration.'
                ];
            }

            // Step 3: Issue SSL certificate automatically
            $sslResult = $this->autoIssueSSL($domain);

            // Step 4: Update domain status based on SSL result
            $domain->domain_verified = true;
            $domain->is_active = true;
            $domain->verified_at = now();

            // Update SSL status based on actual result
            if ($sslResult['status'] === 'success') {
                $domain->ssl_status = 'active';
                if ($sslResult['method'] === 'letsencrypt') {
                    $domain->ssl_issued_at = now();
                    $domain->ssl_expires_at = now()->addDays(90);
                }
            } else {
                $domain->ssl_status = 'failed';
                Log::warning("SSL issuance failed for {$domain->custom_domain}: " . $sslResult['message']);

                // If SSL completely failed, configure for HTTP only
                if ($sslResult['status'] === 'failed') {
                    $this->configureHttpOnly($domain->custom_domain);
                }
            }

            $domain->save();

            // Step 5: Clear domain cache
            \Modules\Superadmin\Http\Middleware\CustomDomainResolver::clearDomainCache($domain->custom_domain);

            // Step 6: Auto-fix any remaining issues
            $this->autoFixDomainIssues($domain);

            Log::info("Domain {$domain->custom_domain} configured successfully with SSL status: {$domain->ssl_status}");

            $message = $sslResult['status'] === 'success'
                ? 'Domain configured successfully! Your custom domain is now active with SSL certificate.'
                : 'Domain configured successfully! SSL certificate will be retried automatically.';

            return [
                'success' => true,
                'message' => $message,
                'domain' => $domain->custom_domain,
                'ssl_status' => $domain->fresh()->ssl_status,
                'is_active' => true,
                'ssl_details' => $sslResult
            ];

        } catch (\Exception $e) {
            Log::error("Auto-configuration failed for {$domain->custom_domain}: " . $e->getMessage());

            return [
                'success' => false,
                'step' => 'general_error',
                'message' => 'Configuration failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Create Apache virtual host with SSL support
     */
    private function createVirtualHostWithSSL(CustomDomain $domain)
    {
        try {
            $domainName = $domain->custom_domain;
            Log::info("Creating virtual host with SSL for: {$domainName}");

            // Generate configuration
            $configContent = $this->generateVhostConfigWithSSL($domainName);
            Log::info("Generated configuration for: {$domainName}");

            // Write config file using sudo
            $configFile = $this->vhostPath . $domainName . '.conf';

            // Create temporary file first
            $tempFile = sys_get_temp_dir() . '/' . $domainName . '.conf.tmp';
            File::put($tempFile, $configContent);
            Log::info("Temporary configuration file created: {$tempFile}");

            // Move to Apache directory using sudo
            exec("sudo mv '{$tempFile}' '{$configFile}' 2>&1", $output, $returnCode);
            if ($returnCode !== 0) {
                throw new \Exception("Failed to move config file to Apache directory: " . implode("\n", $output));
            }

            // Set proper permissions
            exec("sudo chown root:root '{$configFile}' 2>&1", $output, $returnCode);
            exec("sudo chmod 644 '{$configFile}' 2>&1", $output, $returnCode);

            Log::info("Configuration file written: {$configFile}");

            // Verify file was created
            if (!file_exists($configFile)) {
                throw new \Exception("Configuration file was not created: {$configFile}");
            }

            // Enable site
            $this->enableSite($domainName);

            // Reload Apache
            $this->reloadApache();

            Log::info("Virtual host with SSL created successfully for: {$domainName}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to create virtual host with SSL for {$domain->custom_domain}: " . $e->getMessage());
            Log::error("Stack trace: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Generate Apache virtual host configuration with SSL
     */
    private function generateVhostConfigWithSSL($domainName)
    {
        $documentRoot = base_path('public');

        return "
<VirtualHost *:80>
    ServerName {$domainName}
    ServerAlias www.{$domainName}
    DocumentRoot {$documentRoot}

    # Handle Laravel application
    <Directory {$documentRoot}>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>

    # Ensure .well-known directory is accessible for Let's Encrypt
    <Directory {$documentRoot}/.well-known>
        Options -Indexes +FollowSymLinks
        AllowOverride None
        Require all granted
    </Directory>

    # Allow Let's Encrypt challenges before any redirects
    RewriteEngine On

    # Explicitly allow .well-known directory for Let's Encrypt challenges
    RewriteCond %{REQUEST_URI} ^/\\.well-known/
    RewriteRule ^(.*)$ - [L]

    # Redirect to HTTPS for all other requests
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    ErrorLog \${APACHE_LOG_DIR}/{$domainName}_error.log
    CustomLog \${APACHE_LOG_DIR}/{$domainName}_access.log combined
</VirtualHost>

<IfModule mod_ssl.c>
<VirtualHost *:443>
    ServerName {$domainName}
    ServerAlias www.{$domainName}
    DocumentRoot {$documentRoot}

    # Handle Laravel application
    <Directory {$documentRoot}>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>

    # SSL Configuration (will be updated after certificate issuance)
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/ssl-cert-snakeoil.pem
    SSLCertificateKeyFile /etc/ssl/private/ssl-cert-snakeoil.key

    # SSL Security
    SSLProtocol all -SSLv2 -SSLv3
    SSLCipherSuite ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305
    SSLHonorCipherOrder on

    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection \"1; mode=block\"
    Header always set Strict-Transport-Security \"max-age=63072000; includeSubDomains; preload\"

    ErrorLog \${APACHE_LOG_DIR}/{$domainName}_ssl_error.log
    CustomLog \${APACHE_LOG_DIR}/{$domainName}_ssl_access.log combined
</VirtualHost>
</IfModule>";
    }

    /**
     * Automatically issue SSL certificate
     */
    private function autoIssueSSL(CustomDomain $domain)
    {
        try {
            $domainName = $domain->custom_domain;
            Log::info("Auto-issuing SSL certificate for: {$domainName}");

            // Check if we have a wildcard certificate first
            $wildcardResult = $this->useWildcardSSL($domainName);
            if ($wildcardResult['success']) {
                return $wildcardResult;
            }

            // Try Let's Encrypt first
            $letsEncryptResult = $this->issueLetsEncryptSSL($domainName);
            if ($letsEncryptResult['success']) {
                // Try to update Apache config multiple times with delays
                $configUpdated = false;
                $maxRetries = 3;

                for ($i = 0; $i < $maxRetries; $i++) {
                    Log::info("Attempting Apache SSL config update for {$domainName} (attempt " . ($i + 1) . "/{$maxRetries})");

                    if ($this->updateApacheSSLConfig($domainName)) {
                        $configUpdated = true;
                        break;
                    }

                    if ($i < $maxRetries - 1) {
                        Log::info("Retrying Apache SSL config update in 3 seconds...");
                        sleep(3);
                    }
                }

                if ($configUpdated) {
                    return [
                        'method' => 'letsencrypt',
                        'status' => 'success',
                        'message' => 'Let\'s Encrypt SSL certificate issued and configured successfully'
                    ];
                } else {
                    // Try manual fix as last resort
                    Log::info("Attempting manual SSL configuration fix for {$domainName}");
                    if ($this->manualSSLConfigFix($domainName)) {
                        return [
                            'method' => 'letsencrypt',
                            'status' => 'success',
                            'message' => 'Let\'s Encrypt SSL certificate issued and configured successfully (manual fix applied)'
                        ];
                    }

                    return [
                        'method' => 'letsencrypt',
                        'status' => 'partial',
                        'message' => 'SSL certificate issued but Apache configuration update failed'
                    ];
                }
            } else {
                // Let's Encrypt failed, try to use main domain certificate
                Log::warning("Let's Encrypt failed for {$domainName}, trying main domain certificate: " . $letsEncryptResult['message']);

                $mainDomainResult = $this->useMainDomainSSL($domainName);
                if ($mainDomainResult['success']) {
                    return $mainDomainResult;
                }

                // Try using one of the existing certificates as fallback
                $fallbackResult = $this->useFallbackSSL($domainName);
                if ($fallbackResult['success']) {
                    return $fallbackResult;
                }

                return [
                    'method' => 'none',
                    'status' => 'failed',
                    'message' => 'SSL certificate setup failed. Domain configured for HTTP only.'
                ];
            }

        } catch (\Exception $e) {
            Log::error("Auto SSL issuance failed for {$domain->custom_domain}: " . $e->getMessage());

            $domain->ssl_status = 'failed';
            $domain->save();

            return [
                'method' => 'failed',
                'status' => 'error',
                'message' => 'SSL issuance failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Issue SSL certificate (simplified version)
     */
    private function issueSimpleSSL(CustomDomain $domain)
    {
        try {
            // Create SSL directory
            $sslDir = $this->sslPath . $domain->custom_domain;
            if (!File::exists($sslDir)) {
                File::makeDirectory($sslDir, 0755, true);
            }

            // For now, we'll use a self-signed certificate or skip SSL
            // In production, integrate with Let's Encrypt
            $domain->ssl_status = 'active';
            $domain->ssl_issued_at = now();
            $domain->ssl_expires_at = now()->addDays(90);
            $domain->save();

            Log::info("SSL certificate issued for {$domain->custom_domain}");

        } catch (\Exception $e) {
            Log::error("SSL issuance failed for {$domain->custom_domain}: " . $e->getMessage());
            $domain->ssl_status = 'failed';
            $domain->save();
        }
    }

    /**
     * Get comprehensive DNS instructions with provider-specific guides
     */
    public function getDnsInstructions($domainName)
    {
        $serverIp = $this->getServerIp();
        $rootDomain = $this->extractRootDomain($domainName);
        $isSubdomain = $domainName !== $rootDomain;

        return [
            'domain' => $domainName,
            'root_domain' => $rootDomain,
            'is_subdomain' => $isSubdomain,
            'server_ip' => $serverIp,
            'dns_records' => [
                [
                    'type' => 'A',
                    'name' => $isSubdomain ? str_replace('.' . $rootDomain, '', $domainName) : '@',
                    'value' => $serverIp,
                    'ttl' => 300,
                    'priority' => 1,
                    'description' => $isSubdomain ? 'Points your subdomain to our server' : 'Points your domain to our server'
                ],
                [
                    'type' => 'A',
                    'name' => $isSubdomain ? 'www.' . str_replace('.' . $rootDomain, '', $domainName) : 'www',
                    'value' => $serverIp,
                    'ttl' => 300,
                    'priority' => 2,
                    'description' => 'Points www version to our server (recommended)'
                ]
            ],
            'providers' => $this->getDnsProviderInstructions($domainName, $serverIp),
            'verification' => [
                'methods' => [
                    [
                        'name' => 'Online DNS Checker',
                        'url' => "https://dnschecker.org/#A/{$domainName}",
                        'description' => 'Check if your DNS changes have propagated worldwide'
                    ],
                    [
                        'name' => 'Command Line (Windows)',
                        'command' => "nslookup {$domainName}",
                        'description' => 'Open Command Prompt and run this command'
                    ],
                    [
                        'name' => 'Command Line (Mac/Linux)',
                        'command' => "dig {$domainName}",
                        'description' => 'Open Terminal and run this command'
                    ]
                ],
                'expected_result' => $serverIp,
                'propagation_time' => '5-30 minutes (can take up to 48 hours in rare cases)'
            ],
            'troubleshooting' => [
                'common_issues' => [
                    [
                        'issue' => 'DNS not propagating',
                        'solution' => 'Wait 15-30 minutes and try again. DNS changes can take time to propagate globally.'
                    ],
                    [
                        'issue' => 'Wrong IP address showing',
                        'solution' => 'Double-check you entered the correct IP address: ' . $serverIp
                    ],
                    [
                        'issue' => 'Cloudflare proxy enabled',
                        'solution' => 'Make sure the orange cloud is GRAY (DNS only) for initial setup, not orange (proxied).'
                    ]
                ],
                'support' => [
                    'message' => 'Need help? Our support team is here to assist you!',
                    'contact_methods' => [
                        'email' => '<EMAIL>',
                        'live_chat' => 'Available 24/7 through the support widget',
                        'documentation' => 'Check our knowledge base for detailed guides'
                    ]
                ]
            ]
        ];
    }

    /**
     * Extract root domain from subdomain
     */
    private function extractRootDomain($domain)
    {
        $parts = explode('.', $domain);
        if (count($parts) <= 2) {
            return $domain; // Already root domain
        }

        // Return last two parts (domain.com from sub.domain.com)
        return implode('.', array_slice($parts, -2));
    }

    /**
     * Get DNS provider-specific instructions
     */
    private function getDnsProviderInstructions($domainName, $serverIp)
    {
        $rootDomain = $this->extractRootDomain($domainName);
        $isSubdomain = $domainName !== $rootDomain;
        $recordName = $isSubdomain ? str_replace('.' . $rootDomain, '', $domainName) : '@';

        return [
            'cloudflare' => [
                'name' => 'Cloudflare',
                'logo' => 'https://www.cloudflare.com/img/cf-facebook-card.png',
                'popularity' => 'Most Popular',
                'steps' => [
                    [
                        'step' => 1,
                        'title' => 'Login to Cloudflare Dashboard',
                        'description' => 'Go to https://dash.cloudflare.com and login to your account',
                        'image' => '/images/dns-guides/cloudflare-login.png'
                    ],
                    [
                        'step' => 2,
                        'title' => 'Select Your Domain',
                        'description' => "Click on your domain ({$rootDomain}) from the list",
                        'image' => '/images/dns-guides/cloudflare-select-domain.png'
                    ],
                    [
                        'step' => 3,
                        'title' => 'Go to DNS Settings',
                        'description' => 'Click on the "DNS" tab in the top menu',
                        'image' => '/images/dns-guides/cloudflare-dns-tab.png'
                    ],
                    [
                        'step' => 4,
                        'title' => 'Add A Record',
                        'description' => 'Click "Add record" button and fill in the details:',
                        'details' => [
                            'Type: A',
                            "Name: {$recordName}",
                            "IPv4 address: {$serverIp}",
                            'TTL: Auto',
                            'Proxy status: DNS only (gray cloud) ⚠️ IMPORTANT'
                        ],
                        'warning' => 'Make sure the cloud is GRAY (DNS only), not orange (proxied)!'
                    ],
                    [
                        'step' => 5,
                        'title' => 'Add WWW Record (Optional)',
                        'description' => 'Add another A record for www version:',
                        'details' => [
                            'Type: A',
                            'Name: www' . ($isSubdomain ? '.' . $recordName : ''),
                            "IPv4 address: {$serverIp}",
                            'TTL: Auto',
                            'Proxy status: DNS only (gray cloud)'
                        ]
                    ],
                    [
                        'step' => 6,
                        'title' => 'Save and Wait',
                        'description' => 'Click "Save" and wait 5-15 minutes for DNS propagation'
                    ]
                ],
                'video_tutorial' => 'https://www.youtube.com/watch?v=cloudflare-dns-setup',
                'notes' => [
                    '⚠️ CRITICAL: Make sure proxy is disabled (gray cloud) during initial setup',
                    '✅ You can enable proxy (orange cloud) after domain is verified and working',
                    '🕐 DNS changes typically take 5-15 minutes with Cloudflare'
                ]
            ],
            'godaddy' => [
                'name' => 'GoDaddy',
                'logo' => 'https://img6.wsimg.com/ux/favicon/favicon-96x96.png',
                'steps' => [
                    [
                        'step' => 1,
                        'title' => 'Login to GoDaddy',
                        'description' => 'Go to https://account.godaddy.com and login'
                    ],
                    [
                        'step' => 2,
                        'title' => 'Manage DNS',
                        'description' => "Find your domain ({$rootDomain}) and click 'Manage DNS'"
                    ],
                    [
                        'step' => 3,
                        'title' => 'Add A Record',
                        'description' => 'Click "Add" and create an A record:',
                        'details' => [
                            'Type: A',
                            "Name: {$recordName}",
                            "Value: {$serverIp}",
                            'TTL: 1 Hour'
                        ]
                    ],
                    [
                        'step' => 4,
                        'title' => 'Save Changes',
                        'description' => 'Click "Save" and wait 10-30 minutes for propagation'
                    ]
                ]
            ],
            'namecheap' => [
                'name' => 'Namecheap',
                'logo' => 'https://www.namecheap.com/assets/img/nc-icon/favicon-96x96.png',
                'steps' => [
                    [
                        'step' => 1,
                        'title' => 'Login to Namecheap',
                        'description' => 'Go to https://ap.www.namecheap.com/login and login'
                    ],
                    [
                        'step' => 2,
                        'title' => 'Domain List',
                        'description' => "Go to 'Domain List' and find your domain ({$rootDomain})"
                    ],
                    [
                        'step' => 3,
                        'title' => 'Manage DNS',
                        'description' => 'Click "Manage" next to your domain'
                    ],
                    [
                        'step' => 4,
                        'title' => 'Advanced DNS',
                        'description' => 'Click on "Advanced DNS" tab'
                    ],
                    [
                        'step' => 5,
                        'title' => 'Add A Record',
                        'description' => 'Click "Add New Record" and create:',
                        'details' => [
                            'Type: A Record',
                            "Host: {$recordName}",
                            "Value: {$serverIp}",
                            'TTL: Automatic'
                        ]
                    ]
                ]
            ],
            'generic' => [
                'name' => 'Other DNS Providers',
                'description' => 'For any other DNS provider, follow these general steps:',
                'steps' => [
                    [
                        'step' => 1,
                        'title' => 'Access DNS Management',
                        'description' => 'Login to your domain registrar or DNS provider'
                    ],
                    [
                        'step' => 2,
                        'title' => 'Find DNS Settings',
                        'description' => 'Look for "DNS Management", "DNS Records", or "Zone File"'
                    ],
                    [
                        'step' => 3,
                        'title' => 'Add A Record',
                        'description' => 'Create a new A record with these details:',
                        'details' => [
                            'Type: A',
                            "Name/Host: {$recordName}",
                            "Value/Points to: {$serverIp}",
                            'TTL: 300 (or lowest available)'
                        ]
                    ],
                    [
                        'step' => 4,
                        'title' => 'Save and Wait',
                        'description' => 'Save changes and wait 15-30 minutes for propagation'
                    ]
                ]
            ]
        ];
    }

    /**
     * Batch check all domains status
     */
    public function checkAllDomainsStatus()
    {
        $domains = CustomDomain::where('is_active', true)->get();
        $results = [];

        foreach ($domains as $domain) {
            $status = $this->checkDomainPointing($domain->custom_domain);

            $results[] = [
                'id' => $domain->id,
                'domain' => $domain->custom_domain,
                'business_id' => $domain->business_id,
                'pointing_status' => $status,
                'ssl_status' => $domain->ssl_status,
                'verified' => $domain->domain_verified
            ];
        }

        return $results;
    }

    /**
     * Remove domain configuration completely
     */
    public function removeDomainConfig(CustomDomain $domain)
    {
        try {
            $domainName = $domain->custom_domain;
            $removed = false;

            // Step 1: Disable Apache site
            try {
                exec("sudo a2dissite {$domainName}.conf 2>&1", $output, $returnCode);
                if ($returnCode === 0) {
                    Log::info("Apache site disabled for: {$domainName}");
                } else {
                    Log::warning("Failed to disable Apache site for {$domainName}: " . implode("\n", $output));
                }
            } catch (\Exception $e) {
                Log::warning("Error disabling Apache site for {$domainName}: " . $e->getMessage());
            }

            // Step 2: Remove Apache configuration files using sudo
            $configFile = $this->vhostPath . $domainName . '.conf';
            if (file_exists($configFile)) {
                exec("sudo rm -f '{$configFile}' 2>&1", $output, $returnCode);
                if ($returnCode === 0) {
                    Log::info("Apache config file removed: {$configFile}");
                    $removed = true;
                } else {
                    Log::warning("Failed to remove Apache config file: " . implode("\n", $output));
                }
            }

            // Step 3: Remove from sites-enabled if still there
            $enabledFile = '/etc/apache2/sites-enabled/' . $domainName . '.conf';
            if (file_exists($enabledFile)) {
                exec("sudo rm -f '{$enabledFile}' 2>&1", $output, $returnCode);
                if ($returnCode === 0) {
                    Log::info("Apache enabled site removed: {$enabledFile}");
                } else {
                    Log::warning("Failed to remove enabled site: " . implode("\n", $output));
                }
            }

            // Step 4: Reload Apache
            try {
                $this->reloadApache();
                Log::info("Apache reloaded after removing {$domainName}");
            } catch (\Exception $e) {
                Log::warning("Failed to reload Apache after removing {$domainName}: " . $e->getMessage());
            }

            // Step 5: Clear domain cache
            \Modules\Superadmin\Http\Middleware\CustomDomainResolver::clearDomainCache($domainName);

            Log::info("Domain configuration completely removed for: {$domainName}");
            return $removed;

        } catch (\Exception $e) {
            Log::error("Failed to remove domain config for {$domain->custom_domain}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Issue Let's Encrypt SSL certificate
     */
    private function issueLetsEncryptSSL($domainName)
    {
        try {
            $webroot = base_path('public');
            $certbotCmd = $this->findCertbotCommand();

            if (!$certbotCmd) {
                Log::warning("Certbot not found, cannot issue Let's Encrypt certificate");
                return [
                    'success' => false,
                    'message' => 'Certbot not found on server'
                ];
            }

            // Ensure .well-known directory exists and is writable
            $wellKnownDir = $webroot . '/.well-known/acme-challenge';
            if (!file_exists($wellKnownDir)) {
                exec("sudo mkdir -p '{$wellKnownDir}' && sudo chmod 755 '{$wellKnownDir}' && sudo chown www-data:www-data '{$wellKnownDir}'");
            }

            $command = sprintf(
                'sudo %s certonly --webroot --webroot-path=%s --email <EMAIL> --agree-tos --no-eff-email --domains %s --non-interactive --expand --force-renewal 2>&1',
                $certbotCmd,
                $webroot,
                $domainName
            );

            Log::info("Running certbot command for {$domainName}");
            exec($command, $output, $returnCode);

            if ($returnCode === 0) {
                Log::info("Let's Encrypt certificate issued successfully for: {$domainName}");
                return [
                    'success' => true,
                    'message' => 'SSL certificate issued successfully'
                ];
            } else {
                $errorMessage = implode("\n", $output);
                Log::warning("Let's Encrypt failed for {$domainName}: " . $errorMessage);
                return [
                    'success' => false,
                    'message' => $this->parseCertbotError($errorMessage)
                ];
            }

        } catch (\Exception $e) {
            Log::error("Let's Encrypt SSL issuance failed for {$domainName}: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'SSL issuance failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Parse certbot error messages for user-friendly display
     */
    private function parseCertbotError($errorMessage)
    {
        if (strpos($errorMessage, 'Permission denied') !== false) {
            return 'Permission error - server configuration issue';
        }

        if (strpos($errorMessage, 'timeout') !== false) {
            return 'Connection timeout - please check domain DNS';
        }

        if (strpos($errorMessage, 'too many requests') !== false) {
            return 'Rate limit reached - please try again later';
        }

        if (strpos($errorMessage, 'unauthorized') !== false) {
            return 'Domain validation failed - check DNS configuration';
        }

        if (strpos($errorMessage, 'connection refused') !== false) {
            return 'Cannot connect to domain - check DNS and firewall';
        }

        // Return first line of error for other cases
        $lines = explode("\n", $errorMessage);
        return isset($lines[0]) ? trim($lines[0]) : 'SSL certificate issuance failed';
    }

    /**
     * Manual SSL configuration fix as last resort
     */
    private function manualSSLConfigFix($domainName)
    {
        try {
            $configFile = $this->vhostPath . $domainName . '.conf';
            $certPath = "/etc/letsencrypt/live/{$domainName}";

            Log::info("Attempting manual SSL fix for {$domainName}");

            // Wait a bit longer for certificate files to be ready
            sleep(5);

            // Check if certificate directory exists
            exec("sudo test -d '{$certPath}' 2>&1", $dirOutput, $dirReturn);
            if ($dirReturn !== 0) {
                Log::error("Certificate directory does not exist: {$certPath}");
                return false;
            }

            // Force update SSL configuration
            exec("sudo sed -i 's|SSLCertificateFile.*|SSLCertificateFile {$certPath}/fullchain.pem|g' '{$configFile}' 2>&1", $output1, $returnCode1);
            exec("sudo sed -i 's|SSLCertificateKeyFile.*|SSLCertificateKeyFile {$certPath}/privkey.pem|g' '{$configFile}' 2>&1", $output2, $returnCode2);

            if ($returnCode1 !== 0 || $returnCode2 !== 0) {
                Log::error("Manual SSL fix failed for {$domainName}: " . implode("\n", array_merge($output1, $output2)));
                return false;
            }

            // Test Apache configuration
            exec("sudo apache2ctl configtest 2>&1", $testOutput, $testReturn);
            if ($testReturn !== 0) {
                Log::error("Apache config test failed after manual SSL fix: " . implode("\n", $testOutput));
                return false;
            }

            // Reload Apache
            $this->reloadApache();

            Log::info("Manual SSL configuration fix successful for {$domainName}");
            return true;

        } catch (\Exception $e) {
            Log::error("Manual SSL fix exception for {$domainName}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Auto-fix common domain issues after configuration
     */
    private function autoFixDomainIssues($domain)
    {
        try {
            $domainName = $domain->custom_domain;
            $configFile = $this->vhostPath . $domainName . '.conf';

            // Check if config file exists
            if (!file_exists($configFile)) {
                Log::warning("Config file not found for auto-fix: {$configFile}");
                return;
            }

            // Read current config
            $configContent = file_get_contents($configFile);
            $needsReload = false;

            // Fix 1: Remove PHP-FPM if present (use mod_php instead)
            if (strpos($configContent, 'php-fpm.sock') !== false) {
                Log::info("Removing PHP-FPM configuration from {$domainName}");
                exec("sudo sed -i '/FilesMatch.*php/,+2d' '{$configFile}' 2>&1");
                $needsReload = true;
            }

            // Fix 2: Update SSL certificate if Let's Encrypt is available
            $certPath = "/etc/letsencrypt/live/{$domainName}";
            if (file_exists($certPath . '/fullchain.pem') && strpos($configContent, 'ssl-cert-snakeoil') !== false) {
                Log::info("Updating SSL certificate configuration for {$domainName}");
                exec("sudo sed -i 's|SSLCertificateFile /etc/ssl/certs/ssl-cert-snakeoil.pem|SSLCertificateFile {$certPath}/fullchain.pem|g' '{$configFile}' 2>&1");
                exec("sudo sed -i 's|SSLCertificateKeyFile /etc/ssl/private/ssl-cert-snakeoil.key|SSLCertificateKeyFile {$certPath}/privkey.pem|g' '{$configFile}' 2>&1");
                $needsReload = true;
            }

            // Reload Apache if changes were made
            if ($needsReload) {
                $this->reloadApache();
                Log::info("Apache reloaded after auto-fixing {$domainName}");
            }

        } catch (\Exception $e) {
            Log::warning("Auto-fix failed for {$domain->custom_domain}: " . $e->getMessage());
        }
    }

    /**
     * Use wildcard SSL certificate if available
     */
    private function useWildcardSSL($domainName)
    {
        try {
            // Check if we have a wildcard certificate for the domain
            $baseDomain = $this->getBaseDomain($domainName);
            $wildcardCertPath = "/etc/letsencrypt/live/*." . $baseDomain;

            // Check for existing wildcard certificate
            $wildcardDirs = glob("/etc/letsencrypt/live/*." . $baseDomain);

            if (!empty($wildcardDirs)) {
                $wildcardDir = $wildcardDirs[0];
                Log::info("Found wildcard certificate for {$domainName}: {$wildcardDir}");

                if ($this->updateApacheSSLConfigWithPath($domainName, $wildcardDir)) {
                    return [
                        'method' => 'wildcard',
                        'status' => 'success',
                        'message' => 'Using existing wildcard SSL certificate'
                    ];
                }
            }

            // Try to create a wildcard certificate
            return $this->createWildcardSSL($baseDomain);

        } catch (\Exception $e) {
            Log::error("Wildcard SSL check failed for {$domainName}: " . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Create a wildcard SSL certificate
     */
    private function createWildcardSSL($baseDomain)
    {
        try {
            $certbotCmd = $this->findCertbotCommand();

            if (!$certbotCmd) {
                return ['success' => false, 'message' => 'Certbot not found'];
            }

            // Create wildcard certificate using DNS challenge
            $command = sprintf(
                'sudo %s certonly --manual --preferred-challenges dns --email <EMAIL> --agree-tos --no-eff-email --domains "*.%s" --non-interactive 2>&1',
                $certbotCmd,
                $baseDomain
            );

            Log::info("Creating wildcard certificate for *.{$baseDomain}");
            Log::warning("Note: Wildcard certificates require manual DNS TXT record setup");

            // For now, return false as wildcard requires manual DNS setup
            return [
                'success' => false,
                'message' => 'Wildcard certificate requires manual DNS TXT record setup'
            ];

        } catch (\Exception $e) {
            Log::error("Wildcard SSL creation failed: " . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Use main domain SSL certificate for custom domains
     */
    private function useMainDomainSSL($domainName)
    {
        try {
            $mainDomainCertPath = "/etc/letsencrypt/live/interrandadmin.com";

            // Check if main domain certificate exists using sudo
            exec("sudo test -f '{$mainDomainCertPath}/fullchain.pem' 2>&1", $output, $returnCode);

            if ($returnCode !== 0) {
                Log::warning("Main domain certificate not found at {$mainDomainCertPath}/fullchain.pem");
                return ['success' => false, 'message' => 'Main domain certificate not found'];
            }

            Log::info("Found main domain certificate, configuring for {$domainName}");

            if ($this->updateApacheSSLConfigWithPath($domainName, $mainDomainCertPath)) {
                return [
                    'method' => 'main_domain',
                    'status' => 'success',
                    'message' => 'Using main domain SSL certificate (browsers may show security warning for domain mismatch)'
                ];
            } else {
                return ['success' => false, 'message' => 'Failed to update Apache configuration'];
            }

        } catch (\Exception $e) {
            Log::error("Main domain SSL setup failed for {$domainName}: " . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Use any available SSL certificate as fallback
     */
    private function useFallbackSSL($domainName)
    {
        try {
            // Try to find any existing certificate we can use
            $availableCerts = [
                "/etc/letsencrypt/live/interrandadmin.com",
                "/etc/letsencrypt/live/pos1.loanratelk.top",
                "/etc/letsencrypt/live/testpos.loanratelk.top"
            ];

            foreach ($availableCerts as $certPath) {
                exec("sudo test -f '{$certPath}/fullchain.pem' 2>&1", $output, $returnCode);
                if ($returnCode === 0) {
                    Log::info("Using fallback certificate from {$certPath} for {$domainName}");

                    if ($this->updateApacheSSLConfigWithPath($domainName, $certPath)) {
                        return [
                            'method' => 'fallback',
                            'status' => 'success',
                            'message' => 'Using fallback SSL certificate (browsers will show security warning)'
                        ];
                    }
                }
            }

            Log::warning("No suitable fallback certificate found for {$domainName}");
            return ['success' => false, 'message' => 'No fallback certificate available'];

        } catch (\Exception $e) {
            Log::error("Fallback SSL setup failed for {$domainName}: " . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Configure domain for HTTP only (remove HTTPS redirect)
     */
    private function configureHttpOnly($domainName)
    {
        try {
            $configFile = $this->vhostPath . $domainName . '.conf';

            if (!file_exists($configFile)) {
                Log::warning("Config file not found for HTTP-only setup: {$configFile}");
                return false;
            }

            Log::info("Configuring {$domainName} for HTTP only (removing HTTPS redirect)");

            // Remove HTTPS redirect from HTTP virtual host
            exec("sudo sed -i '/RewriteCond.*HTTPS.*off/,+1d' '{$configFile}' 2>&1", $output1, $returnCode1);

            // Disable SSL virtual host by commenting it out
            exec("sudo sed -i '/<IfModule mod_ssl.c>/,/<\/IfModule>/s/^/#/' '{$configFile}' 2>&1", $output2, $returnCode2);

            if ($returnCode1 === 0 && $returnCode2 === 0) {
                $this->reloadApache();
                Log::info("Domain {$domainName} configured for HTTP only");
                return true;
            } else {
                Log::error("Failed to configure HTTP-only for {$domainName}");
                return false;
            }

        } catch (\Exception $e) {
            Log::error("HTTP-only configuration failed for {$domainName}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get base domain from subdomain
     */
    private function getBaseDomain($domain)
    {
        $parts = explode('.', $domain);
        if (count($parts) >= 2) {
            return implode('.', array_slice($parts, -2));
        }
        return $domain;
    }

    /**
     * Update Apache SSL config with specific certificate path
     */
    private function updateApacheSSLConfigWithPath($domainName, $certPath)
    {
        try {
            $configFile = $this->vhostPath . $domainName . '.conf';

            if (!file_exists($configFile)) {
                Log::error("Config file not found: {$configFile}");
                return false;
            }

            // Update SSL certificate paths
            exec("sudo sed -i 's|SSLCertificateFile /etc/ssl/certs/ssl-cert-snakeoil.pem|SSLCertificateFile {$certPath}/fullchain.pem|g' '{$configFile}' 2>&1", $output1, $returnCode1);
            exec("sudo sed -i 's|SSLCertificateKeyFile /etc/ssl/private/ssl-cert-snakeoil.key|SSLCertificateKeyFile {$certPath}/privkey.pem|g' '{$configFile}' 2>&1", $output2, $returnCode2);

            if ($returnCode1 !== 0 || $returnCode2 !== 0) {
                Log::error("Failed to update SSL config for {$domainName}");
                return false;
            }

            // Reload Apache
            $this->reloadApache();

            Log::info("Apache SSL configuration updated with wildcard certificate for: {$domainName}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to update Apache SSL config: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Find certbot command
     */
    private function findCertbotCommand()
    {
        $paths = ['/usr/bin/certbot', '/snap/bin/certbot', '/usr/local/bin/certbot'];

        foreach ($paths as $path) {
            if (File::exists($path)) {
                return $path;
            }
        }

        return null;
    }

    /**
     * Update Apache SSL configuration to use Let's Encrypt certificate
     */
    private function updateApacheSSLConfig($domainName)
    {
        try {
            $configFile = $this->vhostPath . $domainName . '.conf';
            $certPath = "/etc/letsencrypt/live/{$domainName}";

            // Add a small delay to ensure certificate files are fully written
            sleep(2);

            // Check if certificate exists using multiple methods
            $certExists = false;

            // Method 1: Direct file check with sudo
            exec("sudo test -f '{$certPath}/fullchain.pem' 2>&1", $testOutput, $testReturn);
            if ($testReturn === 0) {
                $certExists = true;
                Log::info("Certificate found via direct test for {$domainName}");
            } else {
                // Method 2: List directory and check for symlinks
                exec("sudo ls -la '{$certPath}/' 2>&1", $listOutput, $listReturn);
                if ($listReturn === 0) {
                    $dirContents = implode("\n", $listOutput);
                    Log::info("Certificate directory contents for {$domainName}: " . $dirContents);

                    // Check if fullchain.pem symlink exists in the listing
                    if (strpos($dirContents, 'fullchain.pem ->') !== false) {
                        $certExists = true;
                        Log::info("Certificate found via directory listing for {$domainName}");
                    }
                }
            }

            if (!$certExists) {
                Log::warning("Let's Encrypt certificate not found for {$domainName} at {$certPath}/fullchain.pem");
                return false;
            }

            Log::info("Found Let's Encrypt certificate for {$domainName}, updating Apache config");

            // Update SSL certificate paths using sed
            exec("sudo sed -i 's|SSLCertificateFile /etc/ssl/certs/ssl-cert-snakeoil.pem|SSLCertificateFile {$certPath}/fullchain.pem|g' '{$configFile}' 2>&1", $output1, $returnCode1);
            exec("sudo sed -i 's|SSLCertificateKeyFile /etc/ssl/private/ssl-cert-snakeoil.key|SSLCertificateKeyFile {$certPath}/privkey.pem|g' '{$configFile}' 2>&1", $output2, $returnCode2);

            if ($returnCode1 !== 0 || $returnCode2 !== 0) {
                Log::error("Failed to update SSL config for {$domainName}: " . implode("\n", array_merge($output1, $output2)));
                return false;
            }

            // Verify the changes were made
            exec("sudo grep 'SSLCertificateFile.*letsencrypt' '{$configFile}' 2>&1", $verifyOutput, $verifyReturn);
            if ($verifyReturn !== 0) {
                Log::warning("SSL certificate paths may not have been updated correctly for {$domainName}");
                // Show what's actually in the config file
                exec("sudo grep 'SSLCertificate' '{$configFile}' 2>&1", $configOutput, $configReturn);
                if ($configReturn === 0) {
                    Log::info("Current SSL config for {$domainName}: " . implode("\n", $configOutput));
                }
                return false;
            }

            // Reload Apache
            $this->reloadApache();

            Log::info("Apache SSL configuration updated successfully for: {$domainName}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to update Apache SSL config for {$domainName}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clean up Apache log files for domain
     */
    private function cleanupLogs($domainName)
    {
        try {
            $logFiles = [
                "/var/log/apache2/{$domainName}_error.log",
                "/var/log/apache2/{$domainName}_access.log",
                "/var/log/apache2/{$domainName}_ssl_error.log",
                "/var/log/apache2/{$domainName}_ssl_access.log"
            ];

            foreach ($logFiles as $logFile) {
                if (File::exists($logFile)) {
                    File::delete($logFile);
                }
            }

            Log::info("Log files cleaned up for: {$domainName}");

        } catch (\Exception $e) {
            Log::warning("Failed to cleanup logs for {$domainName}: " . $e->getMessage());
        }
    }
}
