@extends('layouts.app')
@section('title', 'Custom Domains')

@push('body_class')
{{ isset($isSuperadmin) && $isSuperadmin ? 'superadmin-mode' : 'business-mode' }}
@endpush

@section('css')
<style>
/* Modern Custom Domain Styles - High Specificity */
.content .domain-stats {
    background: white !important;
    border-radius: 15px !important;
    padding: 25px !important;
    margin: 30px 0 !important;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
    border: 1px solid #e9ecef !important;
    width: 100% !important;
}

.content .stat-item {
    text-align: center !important;
    padding: 20px 10px !important;
}

.content .stat-number {
    font-size: 2.5rem !important;
    font-weight: bold !important;
    color: #667eea !important;
    display: block !important;
    line-height: 1 !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    margin: 0 !important;
}

.content .stat-number:hover {
    transform: scale(1.05) !important;
}

.content .stat-label {
    color: #6c757d !important;
    font-size: 0.85rem !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    margin-top: 8px !important;
    display: block !important;
}

.content .domain-table-card {
    background: white !important;
    border-radius: 15px !important;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
    overflow: hidden !important;
    border: 1px solid #e9ecef !important;
    margin: 20px 0 !important;
}

.content .modern-btn {
    border-radius: 20px !important;
    padding: 8px 20px !important;
    font-weight: 600 !important;
    font-size: 0.85rem !important;
    transition: all 0.3s ease !important;
    border: none !important;
    text-decoration: none !important;
}

.content .modern-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

.content .modern-btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4) !important;
    color: white !important;
    text-decoration: none !important;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}

.status-verified {
    background: #28a745;
    color: white;
}

.status-pending {
    background: #ffc107;
    color: #212529;
}

.ssl-active {
    background: #17a2b8;
    color: white;
}

.ssl-pending {
    background: #fd7e14;
    color: white;
}

.ssl-failed {
    background: #dc3545;
    color: white;
}

/* Enhanced Toast Notifications */
.toastr-progress-modal {
    width: 450px !important;
    border-radius: 15px !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
    border: none !important;
}

.toastr-progress-modal .toast-message {
    padding: 0 !important;
}

/* Progress Animation */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.step-item.active {
    animation: pulse 1s ease-in-out;
}

/* Success Animation */
@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

.success-icon {
    animation: bounceIn 0.6s ease-out;
}

.content .empty-state {
    text-align: center !important;
    padding: 60px 20px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 15px !important;
    color: white !important;
    margin: 30px 0 !important;
}

.content .empty-state h3 {
    color: white !important;
    margin-bottom: 15px !important;
}

.content .empty-state p {
    color: rgba(255,255,255,0.9) !important;
}

.content .empty-state-icon {
    font-size: 4rem !important;
    margin-bottom: 20px !important;
    opacity: 0.8 !important;
    color: white !important;
}

.content-header .page-header-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 30px 20px !important;
    border-radius: 15px !important;
    margin-bottom: 30px !important;
    width: 100% !important;
}

.content-header .page-header-modern h1 {
    color: white !important;
    margin-bottom: 5px !important;
    font-size: 2rem !important;
}

.content-header .page-header-modern p {
    color: rgba(255,255,255,0.8) !important;
    margin-bottom: 0 !important;
}

/* Table improvements */
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.85rem;
}

.table td {
    vertical-align: middle;
    padding: 15px 12px;
}

/* Button group improvements */
.btn-group .btn {
    margin-right: 5px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* Modal improvements */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.modal-footer {
    border-top: none;
}

/* Form improvements */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.input-group-text {
    border-radius: 10px 0 0 10px;
    border: 2px solid #e9ecef;
    border-right: none;
    background: #f8f9fa;
}

/* Progress Modal Styles */
.toastr-progress-modal {
    width: 500px !important;
    border-radius: 15px !important;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2) !important;
}

.toastr-progress-modal .toast-message {
    padding: 0 !important;
    margin: 0 !important;
}

.step-item {
    transition: all 0.3s ease;
}

.step-item i.fa-check {
    animation: checkmark 0.5s ease-in-out;
}

@keyframes checkmark {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Access Type Badge Styles */
.access-badge {
    font-size: 0.6em !important;
    vertical-align: top !important;
    margin-left: 10px;
    padding: 4px 8px !important;
    border-radius: 12px !important;
    font-weight: 600 !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-superadmin {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.badge-business {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
}

/* Superadmin specific styling */
.superadmin-mode .page-header-modern {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.superadmin-mode .modern-btn-primary {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.superadmin-mode .modern-btn-primary:hover {
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

/* Business mode styling (default) */
.business-mode .page-header-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Force proper layout structure */
.content {
    padding: 15px !important;
}

.content .row {
    margin: 0 !important;
}

.content .col-xs-12,
.content .col-md-3,
.content .col-md-4,
.content .col-md-6,
.content .col-md-8,
.content .col-sm-6 {
    padding: 0 15px !important;
}

/* Table styling fixes */
.content .table-responsive {
    border: none !important;
    box-shadow: none !important;
}

.content .table {
    margin-bottom: 0 !important;
}

.content .table th {
    border-top: none !important;
    font-weight: 600 !important;
    color: #495057 !important;
    font-size: 0.85rem !important;
    padding: 15px 12px !important;
}

.content .table td {
    vertical-align: middle !important;
    padding: 15px 12px !important;
}

/* Alert/Info panel styling */
.content .alert {
    border-radius: 15px !important;
    border: none !important;
    margin-bottom: 20px !important;
    padding: 20px !important;
}

/* Button styling fixes */
.content .btn {
    border-radius: 8px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

.content .btn-lg {
    padding: 12px 24px !important;
    font-size: 1rem !important;
}

/* Badge styling */
.content .badge {
    font-size: 0.75rem !important;
    padding: 6px 12px !important;
    border-radius: 12px !important;
}

/* Text styling */
.content h1, .content h2, .content h3, .content h4, .content h5 {
    font-weight: 600 !important;
}

/* Override any conflicting styles */
.content * {
    box-sizing: border-box !important;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .content-header .page-header-modern {
        padding: 20px 15px !important;
        text-align: center !important;
    }

    .content .domain-stats {
        margin: 15px 0 !important;
    }

    .content .domain-table-card {
        margin: 15px 0 !important;
    }

    .toastr-progress-modal {
        width: 90% !important;
        margin: 0 auto !important;
    }

    .access-badge {
        display: block !important;
        margin: 5px 0 0 0 !important;
        font-size: 0.7em !important;
    }
}
</style>
@endsection

@section('content')
<section class="content-header">
    <div class="page-header-modern" style="background: linear-gradient(135deg, {{ isset($isSuperadmin) && $isSuperadmin ? '#ff6b6b 0%, #ee5a24 100%' : '#667eea 0%, #764ba2 100%' }}) !important; color: white !important; padding: 30px 20px !important; border-radius: 15px !important; margin-bottom: 30px !important;">
        <div class="row">
            <div class="col-md-8">
                <h1 style="color: white !important; margin-bottom: 5px !important; font-size: 2rem !important;">
                    <i class="fas fa-globe"></i> Custom Domains
                    @if(isset($isSuperadmin) && $isSuperadmin)
                        <span class="badge" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important; color: white !important; font-size: 0.6em !important; vertical-align: top !important; margin-left: 10px; padding: 4px 8px !important; border-radius: 12px !important; font-weight: 600 !important;">SUPERADMIN</span>
                    @else
                        <span class="badge" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important; color: white !important; font-size: 0.6em !important; vertical-align: top !important; margin-left: 10px; padding: 4px 8px !important; border-radius: 12px !important; font-weight: 600 !important;">BUSINESS</span>
                    @endif
                </h1>
                <p style="color: rgba(255,255,255,0.8) !important; margin-bottom: 0 !important;">
                    @if(isset($isSuperadmin) && $isSuperadmin)
                        Manage custom domains across all businesses
                    @else
                        Professional branded domains for your business
                    @endif
                </p>
            </div>
            <div class="col-md-4 text-right">
                @if(isset($isSuperadmin) && $isSuperadmin)
                    <button class="btn modern-btn modern-btn-primary btn-lg" id="add-domain-btn" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important; color: white !important; border-radius: 20px !important; padding: 12px 24px !important; font-weight: 600 !important; border: none !important; transition: all 0.3s ease !important;">
                        <i class="fas fa-plus"></i> Add Domain
                    </button>
                    <select class="form-control" id="business-filter" style="display: inline-block; width: 200px; margin-left: 10px; border-radius: 20px !important; padding: 8px 15px !important;">
                        <option value="">All Businesses</option>
                        @foreach(\App\Business::all() as $business)
                            <option value="{{ $business->id }}" {{ request()->get('business_id') == $business->id ? 'selected' : '' }}>
                                {{ $business->name }}
                            </option>
                        @endforeach
                    </select>
                @else
                    @if($canAddMore)
                    <button class="btn modern-btn modern-btn-primary btn-lg" id="add-domain-btn" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; color: white !important; border-radius: 20px !important; padding: 12px 24px !important; font-weight: 600 !important; border: none !important; transition: all 0.3s ease !important;">
                        <i class="fas fa-plus"></i> Add Domain
                    </button>
                    @else
                    <button class="btn btn-light btn-lg" disabled style="border-radius: 20px !important; padding: 12px 24px !important; font-weight: 600 !important;">
                        <i class="fas fa-lock"></i> Limit Reached
                    </button>
                    @endif
                @endif
            </div>
        </div>
    </div>
</section>

@if(isset($subscriptionStatus) && !$subscriptionStatus['has_custom_domain_permission'])
<!-- Subscription Status Alert -->
<section class="content" style="padding-top: 0;">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="alert" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; border: none; border-radius: 15px; padding: 25px; margin-bottom: 30px;">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 style="color: white; margin: 0 0 10px 0;">
                                <i class="fas fa-lock"></i> Custom Domains - Premium Feature
                            </h4>
                            <p style="margin: 0; opacity: 0.9; font-size: 1.1rem;">
                                {{ $subscriptionStatus['message'] }}
                            </p>
                            @if($subscriptionStatus['has_subscription'] && !$subscriptionStatus['is_paid'])
                                <small style="opacity: 0.8; display: block; margin-top: 8px;">
                                    <i class="fas fa-info-circle"></i> You currently have a free/trial subscription. Custom domains require a paid subscription.
                                </small>
                            @endif
                        </div>
                        <div class="col-md-4 text-right">
                            @if($subscriptionStatus['action_needed'] === 'subscribe')
                                <a href="{{ route('subscription.index') }}" class="btn btn-lg" style="background: white; color: #ff6b6b; border: none; border-radius: 25px; padding: 12px 30px; font-weight: bold; text-decoration: none;">
                                    <i class="fas fa-rocket"></i> Subscribe Now
                                </a>
                            @elseif($subscriptionStatus['action_needed'] === 'upgrade_to_paid')
                                <a href="{{ route('subscription.index') }}" class="btn btn-lg" style="background: white; color: #ff6b6b; border: none; border-radius: 25px; padding: 12px 30px; font-weight: bold; text-decoration: none;">
                                    <i class="fas fa-arrow-up"></i> Upgrade to Paid
                                </a>
                            @elseif($subscriptionStatus['action_needed'] === 'upgrade_plan')
                                <a href="{{ route('subscription.index') }}" class="btn btn-lg" style="background: white; color: #ff6b6b; border: none; border-radius: 25px; padding: 12px 30px; font-weight: bold; text-decoration: none;">
                                    <i class="fas fa-star"></i> Upgrade Plan
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endif

<!-- Access Type Information Panel -->
<section class="content" style="padding-top: 0;">
    <div class="container-fluid">
        <div class="row">
            <div class="col-xs-12">
                <div class="alert" style="border-radius: 15px !important; border: none !important; margin-bottom: 20px !important; padding: 20px !important;
                    @if(isset($isSuperadmin) && $isSuperadmin)
                        background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(238, 90, 36, 0.1) 100%) !important;
                        border-left: 4px solid #ff6b6b !important;
                    @else
                        background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(0, 242, 254, 0.1) 100%) !important;
                        border-left: 4px solid #4facfe !important;
                    @endif
                ">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 style="margin: 0; color: #333;">
                            @if(isset($isSuperadmin) && $isSuperadmin)
                                <i class="fas fa-user-shield" style="color: #ff6b6b;"></i>
                                <strong>Superadmin Access</strong>
                            @else
                                <i class="fas fa-user-tie" style="color: #4facfe;"></i>
                                <strong>Business User Access</strong>
                            @endif
                        </h5>
                        <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">
                            @if(isset($isSuperadmin) && $isSuperadmin)
                                You are viewing the custom domains management panel with full administrative privileges. You can manage domains across all businesses.
                            @else
                                You are viewing your business custom domains. You can add and manage domains for your business account.
                            @endif
                        </p>
                    </div>
                    <div class="col-md-4 text-right">
                        <span class="badge" style="font-size: 0.9rem; padding: 8px 15px; border-radius: 20px;
                            @if(isset($isSuperadmin) && $isSuperadmin)
                                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                            @else
                                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                            @endif
                            color: white;">
                            @if(isset($isSuperadmin) && $isSuperadmin)
                                <i class="fas fa-crown"></i> Admin Panel
                            @else
                                <i class="fas fa-briefcase"></i> Business Panel
                            @endif
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="content">
    <div class="container-fluid">
        <!-- Modern Stats Section -->
        <div class="domain-stats" style="background: white !important; border-radius: 15px !important; padding: 25px !important; margin: 30px 0 !important; box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important; border: 1px solid #e9ecef !important;">
        <div class="row">
            @if(isset($isSuperadmin) && $isSuperadmin)
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item" style="text-align: center !important; padding: 20px 10px !important;">
                        <span class="stat-number" id="total-count" style="font-size: 2.5rem !important; font-weight: bold !important; color: #667eea !important; display: block !important; line-height: 1 !important;">{{ $stats['total'] }}</span>
                        <span class="stat-label" style="color: #6c757d !important; font-size: 0.85rem !important; text-transform: uppercase !important; letter-spacing: 1px !important; margin-top: 8px !important; display: block !important;">Total Domains</span>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item" style="text-align: center !important; padding: 20px 10px !important;">
                        <span class="stat-number text-success" id="active-count" style="font-size: 2.5rem !important; font-weight: bold !important; color: #28a745 !important; display: block !important; line-height: 1 !important;">{{ $stats['active'] }}</span>
                        <span class="stat-label" style="color: #6c757d !important; font-size: 0.85rem !important; text-transform: uppercase !important; letter-spacing: 1px !important; margin-top: 8px !important; display: block !important;">Active</span>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item" style="text-align: center !important; padding: 20px 10px !important;">
                        <span class="stat-number text-info" id="ssl-count" style="font-size: 2.5rem !important; font-weight: bold !important; color: #17a2b8 !important; display: block !important; line-height: 1 !important;">{{ $stats['ssl_secured'] }}</span>
                        <span class="stat-label" style="color: #6c757d !important; font-size: 0.85rem !important; text-transform: uppercase !important; letter-spacing: 1px !important; margin-top: 8px !important; display: block !important;">SSL Secured</span>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item" style="text-align: center !important; padding: 20px 10px !important;">
                        <span class="stat-number text-warning" id="pending-count" style="font-size: 2.5rem !important; font-weight: bold !important; color: #ffc107 !important; display: block !important; line-height: 1 !important;">{{ $stats['pending'] }}</span>
                        <span class="stat-label" style="color: #6c757d !important; font-size: 0.85rem !important; text-transform: uppercase !important; letter-spacing: 1px !important; margin-top: 8px !important; display: block !important;">Pending</span>
                    </div>
                </div>
            @else
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item" style="text-align: center !important; padding: 20px 10px !important;">
                        <span class="stat-number" id="total-count" style="font-size: 2.5rem !important; font-weight: bold !important; color: #667eea !important; display: block !important; line-height: 1 !important;">{{ $stats['total'] }}</span>
                        <span class="stat-label" style="color: #6c757d !important; font-size: 0.85rem !important; text-transform: uppercase !important; letter-spacing: 1px !important; margin-top: 8px !important; display: block !important;">Total Domains</span>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item" style="text-align: center !important; padding: 20px 10px !important;">
                        <span class="stat-number text-success" id="active-count" style="font-size: 2.5rem !important; font-weight: bold !important; color: #28a745 !important; display: block !important; line-height: 1 !important;">{{ $stats['active'] }}</span>
                        <span class="stat-label" style="color: #6c757d !important; font-size: 0.85rem !important; text-transform: uppercase !important; letter-spacing: 1px !important; margin-top: 8px !important; display: block !important;">Active</span>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item" style="text-align: center !important; padding: 20px 10px !important;">
                        <span class="stat-number text-info" id="ssl-count" style="font-size: 2.5rem !important; font-weight: bold !important; color: #17a2b8 !important; display: block !important; line-height: 1 !important;">{{ $stats['ssl_secured'] }}</span>
                        <span class="stat-label" style="color: #6c757d !important; font-size: 0.85rem !important; text-transform: uppercase !important; letter-spacing: 1px !important; margin-top: 8px !important; display: block !important;">SSL Secured</span>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item" style="text-align: center !important; padding: 20px 10px !important;">
                        <span class="stat-number text-warning" id="remaining-count" style="font-size: 2.5rem !important; font-weight: bold !important; color: #ffc107 !important; display: block !important; line-height: 1 !important;">{{ $stats['remaining'] ?? 0 }}</span>
                        <span class="stat-label" style="color: #6c757d !important; font-size: 0.85rem !important; text-transform: uppercase !important; letter-spacing: 1px !important; margin-top: 8px !important; display: block !important;">Remaining</span>
                    </div>
                </div>
            @endif
            </div>
        </div>

        <!-- Domains Table -->
        <div class="row">
            <div class="col-xs-12">
                <div class="domain-table-card" style="background: white !important; border-radius: 15px !important; box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important; overflow: hidden !important; border: 1px solid #e9ecef !important; margin: 20px 0 !important;">
                <div style="padding: 25px !important;">
                    <div class="row" style="margin-bottom: 20px;">
                        <div class="col-md-8">
                            <h3 style="margin: 0;">
                                <i class="fas fa-list text-primary"></i> Your Custom Domains
                            </h3>
                        </div>
                        <div class="col-md-4 text-right">
                            <button class="btn modern-btn modern-btn-primary" id="refresh-all-status" title="Refresh statistics and table data" style="background: linear-gradient(135deg, {{ isset($isSuperadmin) && $isSuperadmin ? '#ff6b6b 0%, #ee5a24 100%' : '#667eea 0%, #764ba2 100%' }}) !important; color: white !important; border-radius: 20px !important; padding: 8px 20px !important; font-weight: 600 !important; border: none !important; transition: all 0.3s ease !important;">
                                <i class="fas fa-sync"></i> Refresh All
                            </button>
                            <small class="text-muted d-block" style="margin-top: 5px;">
                                Last updated: <span id="last-updated">{{ now()->format('H:i:s') }}</span>
                            </small>
                        </div>
                    </div>

                    @if($currentCount == 0)
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <h3>No Custom Domains Yet</h3>
                        <p style="margin-bottom: 30px;">Add your first custom domain to get started with professional branded access to your system.</p>
                        @if($canAddMore)
                        <button class="btn btn-light btn-lg modern-btn" id="add-first-domain-btn">
                            <i class="fas fa-plus"></i> Add Your First Domain
                        </button>
                        @endif
                    </div>
                    @else
                    <div class="table-responsive">
                        <table class="table table-hover" id="custom_domains_table">
                            <thead>
                                <tr>
                                    @if(isset($isSuperadmin) && $isSuperadmin)
                                        <th>
                                            <i class="fas fa-building"></i> Business
                                        </th>
                                    @endif
                                    <th>
                                        <i class="fas fa-globe"></i> Domain
                                    </th>
                                    <th>
                                        <i class="fas fa-check-circle"></i> Status
                                    </th>
                                    <th>
                                        <i class="fas fa-lock"></i> SSL
                                    </th>
                                    <th>
                                        <i class="fas fa-calendar"></i> Added
                                    </th>
                                    <th width="@if(isset($isSuperadmin) && $isSuperadmin)350px @else 280px @endif">
                                        <i class="fas fa-cogs"></i> Actions
                                    </th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modern Add Domain Modal -->
<div class="modal fade" id="add-domain-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none;">
                <h4 class="modal-title">
                    <i class="fas fa-globe"></i> Add Custom Domain
                </h4>
                <button type="button" class="close" data-dismiss="modal" style="color: white; opacity: 1;">
                    <span>&times;</span>
                </button>
            </div>
            <form id="add-domain-form">
                <div class="modal-body" style="padding: 30px;">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label class="control-label" style="font-weight: 600; color: #333; margin-bottom: 10px;">
                                    <i class="fas fa-globe text-primary"></i> Your Custom Domain
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="input-group input-group-lg">
                                    <span class="input-group-addon">
                                        <i class="fas fa-globe text-primary"></i>
                                    </span>
                                    <input type="text" name="custom_domain" id="custom_domain"
                                           class="form-control"
                                           placeholder="pos.yourcompany.com">
                                </div>
                                <small class="text-muted" style="margin-top: 8px; display: block;">
                                    <i class="fas fa-info-circle"></i>
                                    Any domain extension allowed (.com, .net, .org, .co.uk, .xyz, etc.)
                                </small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box bg-green">
                                <span class="info-box-icon">
                                    <i class="fas fa-magic"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Auto Setup</span>
                                    <span class="info-box-number">Available</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 10px; margin-top: 20px;">
                        <h5><i class="fas fa-rocket"></i> Super Simple Process</h5>
                        <div class="row text-center" style="margin-top: 20px;">
                            <div class="col-md-3">
                                <div style="padding: 15px;">
                                    <i class="fas fa-plus-circle fa-2x" style="margin-bottom: 10px; opacity: 0.8;"></i>
                                    <h6 style="font-weight: bold;">1. Add Domain</h6>
                                    <small style="opacity: 0.8;">Enter your domain</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div style="padding: 15px;">
                                    <i class="fas fa-cog fa-2x" style="margin-bottom: 10px; opacity: 0.8;"></i>
                                    <h6 style="font-weight: bold;">2. Set DNS</h6>
                                    <small style="opacity: 0.8;">Point to our server</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div style="padding: 15px;">
                                    <i class="fas fa-magic fa-2x" style="margin-bottom: 10px; opacity: 0.8;"></i>
                                    <h6 style="font-weight: bold;">3. Auto Setup</h6>
                                    <small style="opacity: 0.8;">We handle everything</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div style="padding: 15px;">
                                    <i class="fas fa-check-circle fa-2x" style="margin-bottom: 10px; opacity: 0.8;"></i>
                                    <h6 style="font-weight: bold;">4. Ready!</h6>
                                    <small style="opacity: 0.8;">Domain is live</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="border-top: none; padding: 20px 30px;">
                    <button type="button" class="btn btn-default btn-lg" data-dismiss="modal">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button type="submit" class="btn modern-btn modern-btn-primary btn-lg">
                        <i class="fas fa-plus"></i> Add Domain
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modern DNS Instructions Modal -->
<div class="modal fade" id="dns-instructions-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content" style="border-radius: 20px; border: none; overflow: hidden;">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 25px;">
                <h4 class="modal-title" style="margin: 0;">
                    <i class="fas fa-rocket"></i> DNS Setup Instructions
                </h4>
                <button type="button" class="close" data-dismiss="modal" style="color: white; opacity: 1; font-size: 1.5rem;">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="dns-instructions-content" style="padding: 30px;">
                <!-- Content will be loaded dynamically -->
            </div>
            <div class="modal-footer" style="border-top: none; padding: 25px;">
                <button type="button" class="btn btn-lg modern-btn modern-btn-primary" data-dismiss="modal">
                    <i class="fas fa-check"></i> Got It!
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@section('javascript')
<script>
$(document).ready(function() {
    // Determine if this is superadmin or business access
    var isSuperadmin = {{ isset($isSuperadmin) && $isSuperadmin ? 'true' : 'false' }};

    // Initialize DataTable only if domains exist
    @if($currentCount > 0)
    var tableColumns = [];

    // Add business column for superadmin
    if (isSuperadmin) {
        tableColumns.push({data: 'business_name', name: 'business_name', orderable: false, searchable: false});
    }

    // Add common columns
    tableColumns.push(
        {data: 'custom_domain', name: 'custom_domain'},
        {data: 'status', name: 'status', orderable: false, searchable: false},
        {data: 'ssl_status', name: 'ssl_status', orderable: false, searchable: false},
        {data: 'verified_at', name: 'verified_at'},
        {data: 'actions', name: 'actions', orderable: false, searchable: false}
    );

    var ajaxUrl = isSuperadmin ?
        "{{ route('superadmin.custom-domains.index') }}" :
        "{{ route('business.custom-domains.index') }}";

    var table = $('#custom_domains_table').DataTable({
        processing: true,
        serverSide: true,
        ajax: ajaxUrl,
        columns: tableColumns,
        order: isSuperadmin ? [[4, 'desc']] : [[3, 'desc']], // Adjust order column based on business column
        language: {
            processing: '<i class="fas fa-spinner fa-spin"></i> Loading domains...'
        },
        drawCallback: function() {
            updateStats();
        }
    });
    @endif

    // Business Filter for Superadmin
    @if(isset($isSuperadmin) && $isSuperadmin)
    $('#business-filter').change(function() {
        var businessId = $(this).val();
        var newUrl = "{{ route('superadmin.custom-domains.index') }}";

        if (businessId) {
            newUrl += "?business_id=" + businessId;
        }

        // Reload the page with the business filter
        window.location.href = newUrl;
    });
    @endif

    // Add Domain Modal
    $('#add-domain-btn, #add-first-domain-btn').click(function() {
        $('#add-domain-modal').modal('show');
    });

    // Add Domain Form Submit
    $('#add-domain-form').submit(function(e) {
        e.preventDefault();

        var formData = {
            custom_domain: $('#custom_domain').val(),
            _token: '{{ csrf_token() }}'
        };

        var storeUrl = isSuperadmin ?
            "{{ route('superadmin.custom-domains.store') }}" :
            "{{ route('business.custom-domains.store') }}";

        $.ajax({
            url: storeUrl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    $('#add-domain-modal').modal('hide');
                    $('#add-domain-form')[0].reset();
                    @if($currentCount > 0)
                    table.ajax.reload();
                    @else
                    location.reload(); // Reload page if this was the first domain
                    @endif
                    toastr.success(response.message);
                } else {
                    toastr.error(response.message);
                }
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        toastr.error(errors[key][0]);
                    });
                } else {
                    toastr.error('An error occurred while adding the domain.');
                }
            }
        });
    });

    // Auto Configure Domain with SSL
    $(document).on('click', '.auto-configure', function() {
        var domainId = $(this).data('id');
        var routePrefix = $(this).data('route-prefix') || (isSuperadmin ? 'superadmin' : 'business');
        var btn = $(this);
        var originalHtml = btn.html();

        // Show loading state
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Setting up SSL...');

        // Show modern progress notification with steps
        var progressHtml = `
            <div style="text-align: center; padding: 20px;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                    <h4 style="margin: 0 0 10px 0;"><i class="fas fa-magic"></i> Auto Setup in Progress</h4>
                    <p style="margin: 0; opacity: 0.9;">Setting up your domain with automatic SSL certificate...</p>
                </div>
                <div class="progress" style="height: 8px; border-radius: 10px; background: #e9ecef; margin-bottom: 20px;">
                    <div class="progress-bar" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); width: 0%; transition: width 0.5s ease; border-radius: 10px;"></div>
                </div>
                <div id="progress-steps" style="text-align: left;">
                    <div class="step-item" data-step="1" style="padding: 8px 0; color: #6c757d;">
                        <i class="fas fa-spinner fa-spin text-primary"></i> Checking domain pointing...
                    </div>
                    <div class="step-item" data-step="2" style="padding: 8px 0; color: #6c757d;">
                        <i class="fas fa-clock text-muted"></i> Creating Apache configuration...
                    </div>
                    <div class="step-item" data-step="3" style="padding: 8px 0; color: #6c757d;">
                        <i class="fas fa-clock text-muted"></i> Issuing SSL certificate...
                    </div>
                    <div class="step-item" data-step="4" style="padding: 8px 0; color: #6c757d;">
                        <i class="fas fa-clock text-muted"></i> Activating domain...
                    </div>
                </div>
            </div>
        `;

        toastr.info(progressHtml, '', {
            timeOut: 0,
            extendedTimeOut: 0,
            closeButton: false,
            allowHtml: true,
            positionClass: 'toast-top-center',
            toastClass: 'toastr-progress-modal'
        });

        // Simulate progress steps
        setTimeout(() => {
            $('.progress-bar').css('width', '25%');
            $('.step-item[data-step="1"] i').removeClass('fa-spinner fa-spin text-primary').addClass('fa-check text-success');
            $('.step-item[data-step="2"] i').removeClass('fa-clock text-muted').addClass('fa-spinner fa-spin text-primary');
        }, 1000);

        setTimeout(() => {
            $('.progress-bar').css('width', '50%');
            $('.step-item[data-step="2"] i').removeClass('fa-spinner fa-spin text-primary').addClass('fa-check text-success');
            $('.step-item[data-step="3"] i').removeClass('fa-clock text-muted').addClass('fa-spinner fa-spin text-primary');
        }, 2000);

        setTimeout(() => {
            $('.progress-bar').css('width', '75%');
            $('.step-item[data-step="3"] i').removeClass('fa-spinner fa-spin text-primary').addClass('fa-check text-success');
            $('.step-item[data-step="4"] i').removeClass('fa-clock text-muted').addClass('fa-spinner fa-spin text-primary');
        }, 3000);

        var autoConfigureUrl = routePrefix === 'superadmin' ?
            "{{ url('superadmin/custom-domains') }}/" + domainId + "/auto-configure" :
            "{{ url('business/custom-domains') }}/" + domainId + "/auto-configure";

        $.ajax({
            url: autoConfigureUrl,
            type: 'POST',
            data: {_token: '{{ csrf_token() }}'},
            timeout: 120000, // 2 minutes timeout
            success: function(response) {
                // Clear the waiting notification
                toastr.clear();

                if (response.success) {
                    // Complete the progress
                    $('.progress-bar').css('width', '100%');
                    $('.step-item[data-step="4"] i').removeClass('fa-spinner fa-spin text-primary').addClass('fa-check text-success');

                    setTimeout(() => {
                        // Clear progress and show success
                        toastr.clear();

                        // Determine SSL status display
                        var sslStatusHtml = '';
                        if (response.ssl_details) {
                            var sslColor = '#17a2b8';
                            var sslIcon = 'fa-shield-alt';
                            var sslBg = '#e7f3ff';

                            if (response.ssl_details.method === 'letsencrypt' && response.ssl_details.status === 'success') {
                                sslColor = '#28a745';
                                sslBg = '#e8f5e8';
                                sslIcon = 'fa-certificate';
                            } else if (response.ssl_details.status === 'partial') {
                                sslColor = '#ffc107';
                                sslBg = '#fff8e1';
                                sslIcon = 'fa-exclamation-triangle';
                            }

                            sslStatusHtml = `
                                <div style="background: ${sslBg}; padding: 15px; border-radius: 10px; border-left: 4px solid ${sslColor}; margin-top: 15px;">
                                    <strong><i class="fas ${sslIcon}" style="color: ${sslColor};"></i> SSL Certificate:</strong>
                                    <span style="color: ${sslColor};">${response.ssl_details.message}</span>
                                </div>
                            `;
                        }

                        var successHtml = `
                            <div style="text-align: center; padding: 20px;">
                                <div style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white; padding: 25px; border-radius: 15px; margin-bottom: 15px;">
                                    <i class="fas fa-check-circle fa-3x" style="margin-bottom: 15px;"></i>
                                    <h3 style="margin: 0 0 10px 0;">🎉 Domain Configured Successfully!</h3>
                                    <p style="margin: 0; opacity: 0.9;">${response.message}</p>
                                </div>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid #28a745;">
                                    <strong><i class="fas fa-globe text-success"></i> Your domain is now live:</strong><br>
                                    <a href="https://${response.domain}" target="_blank" style="color: #007bff; text-decoration: none; font-weight: bold;">
                                        https://${response.domain} <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                                ${sslStatusHtml}
                            </div>
                        `;

                        toastr.success(successHtml, '', {
                            timeOut: 10000,
                            allowHtml: true,
                            positionClass: 'toast-top-center',
                            toastClass: 'toastr-progress-modal'
                        });

                        @if($currentCount > 0)
                        table.ajax.reload();
                        @endif
                        updateStats();
                    }, 1000);
                } else {
                    if (response.step === 'dns_check') {
                        showDnsInstructions(response.instructions.domain, response.instructions);

                        var dnsErrorHtml = `
                            <div style="text-align: center; padding: 20px;">
                                <div style="background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%); color: white; padding: 25px; border-radius: 15px; margin-bottom: 15px;">
                                    <i class="fas fa-exclamation-triangle fa-3x" style="margin-bottom: 15px;"></i>
                                    <h3 style="margin: 0 0 10px 0;">DNS Setup Required</h3>
                                    <p style="margin: 0; opacity: 0.9;">${response.message}</p>
                                </div>
                                <div style="background: #fff3cd; padding: 15px; border-radius: 10px; border-left: 4px solid #ffc107; color: #856404;">
                                    <strong><i class="fas fa-info-circle"></i> Next Step:</strong>
                                    Please update your domain's DNS settings and try again.
                                </div>
                            </div>
                        `;

                        toastr.warning(dnsErrorHtml, '', {
                            timeOut: 15000,
                            allowHtml: true,
                            positionClass: 'toast-top-center',
                            toastClass: 'toastr-progress-modal'
                        });
                    } else {
                        var errorHtml = `
                            <div style="text-align: center; padding: 20px;">
                                <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); color: white; padding: 25px; border-radius: 15px; margin-bottom: 15px;">
                                    <i class="fas fa-times-circle fa-3x" style="margin-bottom: 15px;"></i>
                                    <h3 style="margin: 0 0 10px 0;">Configuration Failed</h3>
                                    <p style="margin: 0; opacity: 0.9;">${response.message}</p>
                                </div>
                                <div style="background: #f8d7da; padding: 15px; border-radius: 10px; border-left: 4px solid #dc3545; color: #721c24;">
                                    <strong><i class="fas fa-lightbulb"></i> What to do:</strong>
                                    Please check your domain settings and try again, or contact support if the issue persists.
                                </div>
                            </div>
                        `;

                        toastr.error(errorHtml, '', {
                            timeOut: 15000,
                            allowHtml: true,
                            positionClass: 'toast-top-center',
                            toastClass: 'toastr-progress-modal'
                        });
                    }
                }
            },
            error: function(xhr) {
                toastr.clear();
                var errorMsg = 'An error occurred during auto-configuration.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                } else if (xhr.status === 0) {
                    errorMsg = 'Connection timeout. Please check your internet connection and try again.';
                } else if (xhr.status >= 500) {
                    errorMsg = 'Server error occurred. Please try again later.';
                }

                var errorHtml = `
                    <div style="text-align: center; padding: 20px;">
                        <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); color: white; padding: 25px; border-radius: 15px; margin-bottom: 15px;">
                            <i class="fas fa-exclamation-circle fa-3x" style="margin-bottom: 15px;"></i>
                            <h3 style="margin: 0 0 10px 0;">Connection Error</h3>
                            <p style="margin: 0; opacity: 0.9;">${errorMsg}</p>
                        </div>
                    </div>
                `;

                toastr.error(errorHtml, '', {
                    timeOut: 10000,
                    allowHtml: true,
                    positionClass: 'toast-top-center',
                    toastClass: 'toastr-progress-modal'
                });
            },
            complete: function() {
                btn.prop('disabled', false).html(originalHtml);
            }
        });
    });

    // Check Domain Status
    $(document).on('click', '.check-status', function() {
        var domainId = $(this).data('id');
        var btn = $(this);

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Checking...');

        var checkStatusUrl = isSuperadmin ?
            "{{ url('superadmin/custom-domains') }}/" + domainId + "/check-status" :
            "{{ url('business/custom-domains') }}/" + domainId + "/check-status";

        $.ajax({
            url: checkStatusUrl,
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    var status = response.status;
                    var message = status.pointing ?
                        'Domain is pointing correctly to our server!' :
                        'Domain is not pointing to our server yet.';

                    if (status.pointing) {
                        toastr.success(message);
                    } else {
                        toastr.warning(message + ' Server IP: ' + status.server_ip);
                    }

                    @if($currentCount > 0)
                    table.ajax.reload();
                    @endif
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('An error occurred during status check.');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-sync"></i> Check Status');
            }
        });
    });

    // View DNS Instructions
    $(document).on('click', '.view-instructions', function() {
        var domainId = $(this).data('id');

        var dnsInstructionsUrl = isSuperadmin ?
            "{{ url('superadmin/custom-domains') }}/" + domainId + "/dns-instructions" :
            "{{ url('business/custom-domains') }}/" + domainId + "/dns-instructions";

        $.ajax({
            url: dnsInstructionsUrl,
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    showDnsInstructions(response.domain, response.instructions);
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('An error occurred while loading DNS instructions.');
            }
        });
    });

    // Issue SSL Certificate
    $(document).on('click', '.issue-ssl', function() {
        var domainId = $(this).data('id');
        var btn = $(this);

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');

        var issueSslUrl = isSuperadmin ?
            "{{ url('superadmin/custom-domains') }}/" + domainId + "/issue-ssl" :
            "{{ url('business/custom-domains') }}/" + domainId + "/issue-ssl";

        $.ajax({
            url: issueSslUrl,
            type: 'POST',
            data: {_token: '{{ csrf_token() }}'},
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    table.ajax.reload();
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('An error occurred while issuing SSL certificate.');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-lock"></i> Issue SSL');
            }
        });
    });

    // Delete Domain
    $(document).on('click', '.delete-domain', function() {
        var domainId = $(this).data('id');
        
        swal({
            title: 'Are you sure?',
            text: 'This will permanently delete the custom domain.',
            icon: 'warning',
            buttons: {
                cancel: {
                    text: 'Cancel',
                    value: null,
                    visible: true,
                    className: 'btn-default'
                },
                confirm: {
                    text: 'Delete',
                    value: true,
                    visible: true,
                    className: 'btn-danger'
                }
            }
        }).then((willDelete) => {
            if (willDelete) {
                var deleteUrl = isSuperadmin ?
                    "{{ url('superadmin/custom-domains') }}/" + domainId :
                    "{{ url('business/custom-domains') }}/" + domainId;

                $.ajax({
                    url: deleteUrl,
                    type: 'DELETE',
                    data: {_token: '{{ csrf_token() }}'},
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            table.ajax.reload();
                        } else {
                            toastr.error(response.message);
                        }
                    },
                    error: function() {
                        toastr.error('An error occurred while deleting the domain.');
                    }
                });
            }
        });
    });

    // Fix Domain Status
    $(document).on('click', '.fix-status', function() {
        var domainId = $(this).data('id');
        var btn = $(this);

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Fixing...');

        var fixStatusUrl = isSuperadmin ?
            "{{ url('superadmin/custom-domains') }}/" + domainId + "/fix-status" :
            "{{ url('business/custom-domains') }}/" + domainId + "/fix-status";

        $.ajax({
            url: fixStatusUrl,
            type: 'POST',
            data: {_token: '{{ csrf_token() }}'},
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    table.ajax.reload();
                    updateStats();
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('An error occurred while fixing the domain status.');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-wrench"></i> Fix Status');
            }
        });
    });

    // Verify Domain (Superadmin only)
    $(document).on('click', '.verify-domain', function() {
        var domainId = $(this).data('id');
        var btn = $(this);

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Verifying...');

        $.ajax({
            url: "{{ url('superadmin/custom-domains') }}/" + domainId + "/verify",
            type: 'POST',
            data: {_token: '{{ csrf_token() }}'},
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    table.ajax.reload();
                    updateStats();
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('An error occurred while verifying the domain.');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-check-circle"></i>');
            }
        });
    });

    function showDnsInstructions(domain, instructions) {
        var serverIp = instructions.server_ip;
        var isSubdomain = instructions.is_subdomain;
        var rootDomain = instructions.root_domain;
        var dnsRecords = instructions.dns_records;
        var providers = instructions.providers;
        var verification = instructions.verification;
        var troubleshooting = instructions.troubleshooting;

        var content = `
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; margin-bottom: 25px; text-align: center;">
                <h3 style="margin: 0; font-size: 1.8rem;">
                    <i class="fas fa-rocket"></i> DNS Setup Instructions
                </h3>
                <p style="margin: 10px 0 0 0; opacity: 0.9;">
                    Point <strong>${domain}</strong> to our server for <strong>FREE SSL certificate</strong>
                </p>
            </div>

            <!-- Quick Summary -->
            <div style="background: #e8f5e8; border: 2px solid #28a745; border-radius: 15px; padding: 20px; margin-bottom: 25px;">
                <h4 style="color: #155724; margin: 0 0 15px 0;">
                    <i class="fas fa-bullseye"></i> What You Need to Do
                </h4>
                <div style="color: #155724;">
                    <strong>Add these DNS records to your domain:</strong>
                    <div style="background: white; border-radius: 8px; padding: 15px; margin-top: 10px;">`;

        // Add DNS records summary
        dnsRecords.forEach(function(record, index) {
            content += `
                        <div style="margin-bottom: ${index < dnsRecords.length - 1 ? '10px' : '0'};">
                            <code style="background: #f8f9fa; padding: 8px 12px; border-radius: 6px; display: inline-block; margin-right: 10px; min-width: 60px; text-align: center; font-weight: bold;">${record.type}</code>
                            <code style="background: #e3f2fd; padding: 8px 12px; border-radius: 6px; display: inline-block; margin-right: 10px; min-width: 100px;">${record.name}</code>
                            <code style="background: #fff3e0; padding: 8px 12px; border-radius: 6px; display: inline-block; font-weight: bold;">${record.value}</code>
                            <small style="color: #6c757d; margin-left: 10px;">${record.description}</small>
                        </div>`;
        });

        content += `
                    </div>
                </div>
            </div>

            <!-- Provider-Specific Instructions -->
            <div class="row">
                <!-- Cloudflare Instructions -->
                <div class="col-md-6">
                    <div style="background: white; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); overflow: hidden; margin-bottom: 20px; border: 2px solid #f76707;">
                        <div style="background: linear-gradient(135deg, #f76707 0%, #fd7e14 100%); color: white; padding: 20px; text-align: center;">
                            <h4 style="margin: 0;">
                                <i class="fab fa-cloudflare"></i> Cloudflare
                            </h4>
                            <small style="opacity: 0.9;">Most Popular Choice</small>
                        </div>
                        <div style="padding: 20px;">`;

        // Add Cloudflare steps
        providers.cloudflare.steps.forEach(function(step, index) {
            content += `
                            <div style="margin-bottom: 15px; padding-bottom: 15px; ${index < providers.cloudflare.steps.length - 1 ? 'border-bottom: 1px solid #eee;' : ''}">
                                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                    <span style="background: #f76707; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; margin-right: 10px;">${step.step}</span>
                                    <strong style="color: #495057;">${step.title}</strong>
                                </div>
                                <p style="margin: 0 0 8px 34px; color: #6c757d; font-size: 14px;">${step.description}</p>`;

            if (step.details) {
                content += `<ul style="margin: 8px 0 0 34px; color: #495057; font-size: 13px;">`;
                step.details.forEach(function(detail) {
                    content += `<li style="margin-bottom: 4px;">${detail}</li>`;
                });
                content += `</ul>`;
            }

            if (step.warning) {
                content += `<div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 8px; margin: 8px 0 0 34px; font-size: 12px; color: #856404;">
                    <i class="fas fa-exclamation-triangle"></i> ${step.warning}
                </div>`;
            }

            content += `</div>`;
        });

        content += `
                            <div style="background: #f8f9fa; border-radius: 8px; padding: 15px; margin-top: 15px;">
                                <h6 style="color: #495057; margin: 0 0 10px 0;">
                                    <i class="fas fa-lightbulb"></i> Pro Tips:
                                </h6>`;

        providers.cloudflare.notes.forEach(function(note) {
            content += `<div style="font-size: 12px; color: #6c757d; margin-bottom: 5px;">${note}</div>`;
        });

        content += `
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other Providers -->
                <div class="col-md-6">
                    <div style="background: white; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); overflow: hidden; margin-bottom: 20px;">
                        <div style="background: linear-gradient(135deg, #495057 0%, #6c757d 100%); color: white; padding: 20px; text-align: center;">
                            <h4 style="margin: 0;">
                                <i class="fas fa-globe"></i> Other DNS Providers
                            </h4>
                            <small style="opacity: 0.9;">GoDaddy, Namecheap, etc.</small>
                        </div>
                        <div style="padding: 20px;">`;

        // Add generic provider steps
        providers.generic.steps.forEach(function(step, index) {
            content += `
                            <div style="margin-bottom: 15px; padding-bottom: 15px; ${index < providers.generic.steps.length - 1 ? 'border-bottom: 1px solid #eee;' : ''}">
                                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                    <span style="background: #6c757d; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; margin-right: 10px;">${step.step}</span>
                                    <strong style="color: #495057;">${step.title}</strong>
                                </div>
                                <p style="margin: 0 0 8px 34px; color: #6c757d; font-size: 14px;">${step.description}</p>`;

            if (step.details) {
                content += `<ul style="margin: 8px 0 0 34px; color: #495057; font-size: 13px;">`;
                step.details.forEach(function(detail) {
                    content += `<li style="margin-bottom: 4px;">${detail}</li>`;
                });
                content += `</ul>`;
            }

            content += `</div>`;
        });

        content += `
                        </div>
                    </div>
                </div>
            </div>

            <!-- Verification Section -->
            <div style="background: #e3f2fd; border: 2px solid #2196f3; border-radius: 15px; padding: 20px; margin-bottom: 25px;">
                <h4 style="color: #1565c0; margin: 0 0 15px 0;">
                    <i class="fas fa-search"></i> Verify Your DNS Setup
                </h4>
                <p style="color: #1976d2; margin-bottom: 15px;">After making DNS changes, verify they're working:</p>

                <div class="row">`;

        verification.methods.forEach(function(method, index) {
            content += `
                    <div class="col-md-4">
                        <div style="background: white; border-radius: 8px; padding: 15px; margin-bottom: 10px;">
                            <h6 style="color: #1565c0; margin: 0 0 8px 0;">${method.name}</h6>
                            <p style="font-size: 12px; color: #6c757d; margin: 0 0 8px 0;">${method.description}</p>`;

            if (method.url) {
                content += `<a href="${method.url}" target="_blank" style="color: #2196f3; font-size: 12px; text-decoration: none;">
                    <i class="fas fa-external-link-alt"></i> Check Now
                </a>`;
            }

            if (method.command) {
                content += `<code style="background: #f5f5f5; padding: 4px 8px; border-radius: 4px; font-size: 11px; display: block; margin-top: 5px;">${method.command}</code>`;
            }

            content += `</div></div>`;
        });

        content += `
                </div>

                <div style="background: white; border-radius: 8px; padding: 15px; margin-top: 15px;">
                    <strong style="color: #1565c0;">Expected Result:</strong>
                    <code style="background: #f5f5f5; padding: 4px 8px; border-radius: 4px; margin-left: 5px;">${verification.expected_result}</code>
                    <br>
                    <small style="color: #6c757d; margin-top: 5px; display: block;">
                        <i class="fas fa-clock"></i> Propagation Time: ${verification.propagation_time}
                    </small>
                </div>
            </div>

            <!-- Troubleshooting Section -->
            <div style="background: #fff3e0; border: 2px solid #ff9800; border-radius: 15px; padding: 20px; margin-bottom: 25px;">
                <h4 style="color: #e65100; margin: 0 0 15px 0;">
                    <i class="fas fa-tools"></i> Troubleshooting
                </h4>`;

        troubleshooting.common_issues.forEach(function(issue) {
            content += `
                <div style="background: white; border-radius: 8px; padding: 15px; margin-bottom: 10px;">
                    <strong style="color: #e65100;">Problem:</strong> ${issue.issue}<br>
                    <strong style="color: #2e7d32;">Solution:</strong> ${issue.solution}
                </div>`;
        });

        content += `
            </div>

            <!-- Support Section -->
            <div style="background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%); color: white; border-radius: 15px; padding: 25px; text-align: center;">
                <h4 style="margin: 0 0 15px 0;">
                    <i class="fas fa-life-ring"></i> Need Help?
                </h4>
                <p style="margin: 0 0 15px 0; opacity: 0.9;">${troubleshooting.support.message}</p>
                <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
                    <div style="background: rgba(255,255,255,0.2); border-radius: 8px; padding: 10px 15px;">
                        <i class="fas fa-envelope"></i> ${troubleshooting.support.contact_methods.email}
                    </div>
                    <div style="background: rgba(255,255,255,0.2); border-radius: 8px; padding: 10px 15px;">
                        <i class="fas fa-comments"></i> ${troubleshooting.support.contact_methods.live_chat}
                    </div>
                </div>
            </div>
        `;

        $('#dns-instructions-content').html(content);
        $('#dns-instructions-modal').modal('show');
    }

    function updateStats() {
        var statsUrl = isSuperadmin ?
            "{{ route('superadmin.custom-domains.stats') }}" :
            "{{ route('business.custom-domains.stats') }}";

        $.ajax({
            url: statsUrl,
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    var stats = response.stats;

                    // Update all stat numbers with animation
                    animateStatUpdate('#total-count', stats.total);
                    animateStatUpdate('#active-count', stats.active);
                    animateStatUpdate('#ssl-count', stats.ssl_secured);

                    if (isSuperadmin) {
                        animateStatUpdate('#pending-count', stats.pending);
                    } else {
                        animateStatUpdate('#remaining-count', stats.remaining || 0);
                    }

                    // Update timestamp
                    var now = new Date();
                    var timeString = now.getHours().toString().padStart(2, '0') + ':' +
                                   now.getMinutes().toString().padStart(2, '0') + ':' +
                                   now.getSeconds().toString().padStart(2, '0');
                    $('#last-updated').text(timeString);
                }
            },
            error: function() {
                console.log('Failed to update statistics');
            }
        });
    }

    function animateStatUpdate(selector, newValue) {
        var $element = $(selector);
        var currentValue = parseInt($element.text()) || 0;

        if (currentValue !== newValue) {
            $element.fadeOut(200, function() {
                $element.text(newValue).fadeIn(200);
            });
        }
    }

    // Refresh all statistics button
    $('#refresh-all-status').click(function() {
        var btn = $(this);
        var originalHtml = btn.html();

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Refreshing...');

        updateStats();

        @if($currentCount > 0)
        if (typeof table !== 'undefined') {
            table.ajax.reload();
        }
        @endif

        setTimeout(function() {
            btn.prop('disabled', false).html(originalHtml);
            toastr.success('Statistics refreshed successfully!');
        }, 1000);
    });
});
</script>
@endsection
