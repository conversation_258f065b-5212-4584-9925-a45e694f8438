#!/bin/bash

# Apache mod_md Setup for Automatic SSL
echo "=== Setting up Apache mod_md for Automatic SSL ==="

# Check if mod_md is available
if ! apache2ctl -M | grep -q "md_module"; then
    echo "📦 Installing mod_md..."
    
    # Enable mod_md (available in Apache 2.4.30+)
    sudo a2enmod md
    sudo a2enmod ssl
    sudo a2enmod rewrite
    
    echo "✅ mod_md enabled"
else
    echo "✅ mod_md already enabled"
fi

# Create mod_md configuration
echo "📝 Creating mod_md configuration..."

sudo tee /etc/apache2/conf-available/mod_md.conf > /dev/null << 'EOF'
# Apache mod_md configuration for automatic SSL

# Global mod_md settings
MDCertificateAgreement accepted
MDContactEmail <EMAIL>
MDStoreDir /etc/apache2/md

# Automatic certificate management
MDRenewMode auto
MDRenewWindow 30d

# Challenge method
MDChallengeType http-01

# Certificate authority
MDCertificateAuthority https://acme-v02.api.letsencrypt.org/directory

# Automatic domain management
MDAutoAdd on

# Log level
LogLevel md:info

# Status endpoint (optional)
<Location "/md-status">
    SetHandler md-status
    Require local
</Location>
EOF

# Enable the configuration
sudo a2enconf mod_md

# Create a template for automatic SSL domains
echo "📄 Creating automatic SSL virtual host template..."

sudo tee /etc/apache2/sites-available/auto-ssl-template.conf > /dev/null << 'EOF'
# Template for automatic SSL domains
# Copy and modify this for each domain

<VirtualHost *:80>
    ServerName DOMAIN_NAME
    DocumentRoot /var/www/interrandadmin.com/public
    
    # Automatic SSL management
    MDomain DOMAIN_NAME
    
    # Redirect to HTTPS (after SSL is obtained)
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # Allow Let's Encrypt challenges
    RewriteCond %{REQUEST_URI} ^/\.well-known/
    RewriteRule ^(.*)$ - [L]
    
    ErrorLog ${APACHE_LOG_DIR}/DOMAIN_NAME_error.log
    CustomLog ${APACHE_LOG_DIR}/DOMAIN_NAME_access.log combined
</VirtualHost>

<VirtualHost *:443>
    ServerName DOMAIN_NAME
    DocumentRoot /var/www/interrandadmin.com/public
    
    # SSL Configuration (managed by mod_md)
    SSLEngine on
    
    # Laravel application configuration
    <Directory /var/www/interrandadmin.com/public>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/DOMAIN_NAME_ssl_error.log
    CustomLog ${APACHE_LOG_DIR}/DOMAIN_NAME_ssl_access.log combined
</VirtualHost>
EOF

# Test Apache configuration
echo "🧪 Testing Apache configuration..."
if sudo apache2ctl configtest; then
    echo "✅ Apache configuration is valid"
    
    # Restart Apache to load mod_md
    echo "🔄 Restarting Apache..."
    sudo systemctl restart apache2
    
    echo "✅ Apache restarted successfully"
else
    echo "❌ Apache configuration test failed"
    exit 1
fi

echo ""
echo "🎉 Apache mod_md setup complete!"
echo ""
echo "📋 How to use:"
echo "   1. Copy the template: sudo cp /etc/apache2/sites-available/auto-ssl-template.conf /etc/apache2/sites-available/yourdomain.com.conf"
echo "   2. Replace DOMAIN_NAME with your actual domain"
echo "   3. Enable the site: sudo a2ensite yourdomain.com.conf"
echo "   4. Reload Apache: sudo systemctl reload apache2"
echo "   5. SSL certificate will be automatically obtained!"
echo ""
echo "📊 Monitor SSL status:"
echo "   - Check certificates: sudo apache2ctl md-status"
echo "   - View logs: sudo tail -f /var/log/apache2/error.log"
echo ""
echo "🔧 Troubleshooting:"
echo "   - Ensure domain points to this server"
echo "   - Check firewall allows port 80 and 443"
echo "   - Verify DNS propagation"
